export const defaultTheme = {
  dark: false,
  roundness: 4,
  version: 3,
  isV3: true,
  colors: {
    primary: '#3268DC',
    primaryContainer: 'rgba(234, 221, 255, 1)',
    secondary: '#757575',
    secondaryContainer: 'rgba(232, 222, 248, 1)',
    tertiary: 'rgba(125, 82, 96, 1)',
    tertiaryContainer: 'rgba(255, 216, 228, 1)',
    surface: '#fff',
    surfaceVariant: 'rgba(231, 224, 236, 1)',
    surfaceDisabled: 'rgba(28, 27, 31, 0.12)',
    background: '#F4F4FA',
    error: '#CB4154',
    errorContainer: 'rgba(249, 222, 220, 1)',
    onPrimary: 'rgba(255, 255, 255, 1)',
    onPrimaryContainer: 'rgba(33, 0, 93, 1)',
    onSecondary: 'rgba(255, 255, 255, 1)',
    onSecondaryContainer: 'rgba(29, 25, 43, 1)',
    onTertiary: 'rgba(255, 255, 255, 1)',
    onTertiaryContainer: 'rgba(49, 17, 29, 1)',
    onSurface: '#333',
    onSurfaceVariant: 'rgba(73, 69, 79, 1)',
    onSurfaceDisabled: 'rgba(28, 27, 31, 0.38)',
    onError: 'rgba(255, 255, 255, 1)',
    onErrorContainer: 'rgba(65, 14, 11, 1)',
    onBackground: 'rgba(28, 27, 31, 1)',
    outline: 'rgba(121, 116, 126, 1)',
    outlineVariant: 'rgba(202, 196, 208, 1)',
    inverseSurface: 'rgba(49, 48, 51, 1)',
    inverseOnSurface: 'rgba(244, 239, 244, 1)',
    inversePrimary: 'rgba(208, 188, 255, 1)',
    shadow: 'rgba(0, 0, 0, 1)',
    scrim: 'rgba(0, 0, 0, 1)',
    backdrop: 'rgba(0,0,0, .5)',
    elevation: {
      level0: 'transparent',
      level1: 'rgb(247, 243, 249)',
      level2: 'rgb(243, 237, 246)',
      level3: 'rgb(238, 232, 244)',
      level4: 'rgb(236, 230, 243)',
      level5: 'rgb(233, 227, 241)',
    },
    brandFontColor: '#3268DC',
    disclaimerColor: '#4EAC56',
    disclaimerSecondary: '#323338',
    buttonPrimary: '#3268DC',
    text: '#333',
    textInfoBackground: '#EAEAF5',
    textNoticeBackground: '#E6EAF8',
    textCriticalBackground: '#CB4154',
    notice: '#3268DC',
    info: '#333',
    critical: '#CB4154',
    border: '#C2C2C2',
    placeholder: '#9F9F9F',
    buttonPrimaryText: '#fff',
    buttonSecondary: '#fff',
    buttonSecondaryText: '#3268DC',
    activeHighlight: '#999',
    disabled: '#C2C2C2',
    brandFontColorSecondary: '#555',
    accent: '#648DE52F',
    backgroundDark: '#252525',
    paper: '#FFFFFF',
    textSecondary: '#555',
    textLight: '#ffffff',
    light: '#f7f7f7',
    evenRow: '#FFFFFF',
    oddRow: '#F4F4FA',
    listHeader: '#FFFFFF',
    logoBackground: '#fff',
    notification: '#397bbd',
    lightGray: '#dedede',
    darkGray: '#333',
    mediumGray: '#aaa',
    set1Color1: '#EF233C',
    set1Color2: '#238571',
    set1Color3: '#F9641F',
    set1Color4: '#231651',
    set1Color5: '#757575',
    set1Color6: '#886B21',
    set1Color7: '#333',
    set1Color8: '#333',
    secondaryTextColor: '#676D79',
  },
  fonts: {
    displayLarge: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
      lineHeight: 64,
      fontSize: 57,
    },
    displayMedium: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
      lineHeight: 52,
      fontSize: 45,
    },
    displaySmall: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
      lineHeight: 44,
      fontSize: 36,
    },
    headlineLarge: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
      lineHeight: 40,
      fontSize: 32,
    },
    headlineMedium: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
      lineHeight: 36,
      fontSize: 28,
    },
    headlineSmall: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
      lineHeight: 32,
      fontSize: 24,
    },
    titleLarge: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
      lineHeight: 28,
      fontSize: 22,
    },
    titleMedium: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.15,
      fontWeight: '500',
      lineHeight: 24,
      fontSize: 16,
    },
    titleSmall: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.1,
      fontWeight: '500',
      lineHeight: 20,
      fontSize: 14,
    },
    labelLarge: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.1,
      fontWeight: '500',
      lineHeight: 20,
      fontSize: 14,
    },
    labelMedium: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.5,
      fontWeight: '500',
      lineHeight: 16,
      fontSize: 12,
    },
    labelSmall: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.5,
      fontWeight: '500',
      lineHeight: 16,
      fontSize: 11,
    },
    bodyLarge: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.15,
      fontWeight: '400',
      lineHeight: 24,
      fontSize: 16,
    },
    bodyMedium: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.25,
      fontWeight: '400',
      lineHeight: 20,
      fontSize: 14,
    },
    bodySmall: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0.4,
      fontWeight: '400',
      lineHeight: 16,
      fontSize: 12,
    },
    default: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      letterSpacing: 0,
      fontWeight: '400',
    },
    regular: {
      fontFamily: 'SourceSans3_400Regular',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'SourceSans3_600SemiBold',
      fontWeight: 'normal',
    },
    bold: {
      fontFamily: 'SourceSans3_700Bold',
      fontWeight: 'normal',
    },
    light: {
      fontFamily: 'SourceSans3_300Light',
      fontWeight: 'normal',
    },
    thin: {
      fontFamily: 'SourceSans3_200ExtraLight',
      fontWeight: 'normal',
    },
    regularTitle: {
      fontFamily: 'Poppins_400Regular',
      fontWeight: 'normal',
    },
    mediumTitle: {
      fontFamily: 'Poppins_600SemiBold',
      fontWeight: 'normal',
    },
    boldTitle: {
      fontFamily: 'Poppins_700Bold',
      fontWeight: 'normal',
    },
    lightTitle: {
      fontFamily: 'Poppins_300Light',
      fontWeight: 'normal',
    },
    thinTitle: {
      fontFamily: 'Poppins_200ExtraLight',
      fontWeight: 'normal',
    },
  },
  animation: {
    scale: 1,
  },
  fontSizes: {
    xxxLarge: {
      fontSize: 48.83,
    },
    xxLarge: {
      fontSize: 39.06,
    },
    xLarge: {
      fontSize: 31.25,
    },
    large: {
      fontSize: 28,
    },
    mediumLarge: {
      fontSize: 25,
    },
    medium: {
      fontSize: 20,
    },
    mediumSmall: {
      fontSize: 18,
    },
    small: {
      fontSize: 16,
    },
    xSmall: {
      fontSize: 14,
    },
    xxSmall: {
      fontSize: 12,
    },
    xxxSmall: {
      fontSize: 10,
    },
  },
  styles: {
    defaultValues: {
      defaultComponentWidth: 400,
      defaultComponentHeight: 38,
      smallComponentWidth: 350,
      largeComponentWidth: 680,
      primaryBorderRadius: 8,
      componentBorderRadius: 5,
      iconButtonSize: 47,
      smallIconButtonSize: 25,
      maxWidth: 1440,
      groupTitleContentWidth: 360,
    },
    borders: {
      primary: {
        borderWidth: 1,
        borderRadius: 8,
        borderColor: '#C2C2C2',
      },
      component: {
        borderWidth: 1,
        borderRadius: 5,
        borderColor: '#C2C2C2',
      },
      None: {
        borderTopWidth: 0,
        borderBottomWidth: 0,
        borderLeftWidth: 0,
        borderRightWidth: 0,
      },
    },
    margins: {
      XS: {
        margin: 3,
      },
      S: {
        margin: 5,
      },
      MS: {
        margin: 8,
      },
      M: {
        margin: 10,
      },
      ML: {
        margin: 20,
      },
      L: {
        margin: 30,
      },
      XL: {
        margin: 80,
      },
      XXL: {
        margin: 160,
      },
      TopXS: {
        marginTop: 3,
      },
      TopS: {
        marginTop: 5,
      },
      TopMS: {
        marginTop: 8,
      },
      TopM: {
        marginTop: 10,
      },
      TopML: {
        marginTop: 20,
      },
      TopL: {
        marginTop: 30,
      },
      TopXL: {
        marginTop: 80,
      },
      TopXXL: {
        marginTop: 160,
      },
      BottomXS: {
        marginBottom: 3,
      },
      BottomS: {
        marginBottom: 5,
      },
      BottomMS: {
        marginBottom: 8,
      },
      BottomM: {
        marginBottom: 10,
      },
      BottomML: {
        marginBottom: 20,
      },
      BottomL: {
        marginBottom: 30,
      },
      BottomXL: {
        marginBottom: 80,
      },
      BottomXXL: {
        marginBottom: 160,
      },
      RightXS: {
        marginRight: 3,
      },
      RightS: {
        marginRight: 5,
      },
      RightMS: {
        marginRight: 8,
      },
      RightM: {
        marginRight: 10,
      },
      RightML: {
        marginRight: 20,
      },
      RightL: {
        marginRight: 30,
      },
      RightXL: {
        marginRight: 80,
      },
      RightXXL: {
        marginRight: 160,
      },
      LeftXS: {
        marginLeft: 3,
      },
      LeftS: {
        marginLeft: 5,
      },
      LeftMS: {
        marginLeft: 8,
      },
      LeftM: {
        marginLeft: 10,
      },
      LeftML: {
        marginLeft: 20,
      },
      LeftL: {
        marginLeft: 30,
      },
      LeftXL: {
        marginLeft: 80,
      },
      LeftXXL: {
        marginLeft: 160,
      },
      VerticalXS: {
        marginVertical: 3,
      },
      VerticalS: {
        marginVertical: 5,
      },
      VerticalMS: {
        marginVertical: 8,
      },
      VerticalM: {
        marginVertical: 10,
      },
      VerticalML: {
        marginVertical: 20,
      },
      VerticalL: {
        marginVertical: 30,
      },
      VerticalXL: {
        marginVertical: 80,
      },
      VerticalXXL: {
        marginVertical: 160,
      },
      HorizontalXS: {
        marginHorizontal: 3,
      },
      HorizontalS: {
        marginHorizontal: 5,
      },
      HorizontalMS: {
        marginHorizontal: 8,
      },
      HorizontalM: {
        marginHorizontal: 10,
      },
      HorizontalML: {
        marginHorizontal: 20,
      },
      HorizontalL: {
        marginHorizontal: 30,
      },
      HorizontalXL: {
        marginHorizontal: 80,
      },
      HorizontalXXL: {
        marginHorizontal: 160,
      },
      None: {
        marginTop: 0,
        marginBottom: 0,
        marginLeft: 0,
        marginRight: 0,
      },
    },
    paddings: {
      XS: {
        padding: 3,
      },
      S: {
        padding: 5,
      },
      MS: {
        padding: 8,
      },
      M: {
        padding: 10,
      },
      ML: {
        padding: 20,
      },
      L: {
        padding: 30,
      },
      XL: {
        padding: 80,
      },
      XXL: {
        padding: 160,
      },
      TopXS: {
        paddingTop: 3,
      },
      TopS: {
        paddingTop: 5,
      },
      TopMS: {
        paddingTop: 8,
      },
      TopM: {
        paddingTop: 10,
      },
      TopML: {
        paddingTop: 20,
      },
      TopL: {
        paddingTop: 30,
      },
      TopXL: {
        paddingTop: 80,
      },
      TopXXL: {
        paddingTop: 160,
      },
      BottomXS: {
        paddingBottom: 3,
      },
      BottomS: {
        paddingBottom: 5,
      },
      BottomMS: {
        paddingBottom: 8,
      },
      BottomM: {
        paddingBottom: 10,
      },
      BottomML: {
        paddingBottom: 20,
      },
      BottomL: {
        paddingBottom: 30,
      },
      BottomXL: {
        paddingBottom: 80,
      },
      BottomXXL: {
        paddingBottom: 160,
      },
      RightXS: {
        paddingRight: 3,
      },
      RightS: {
        paddingRight: 5,
      },
      RightMS: {
        paddingRight: 8,
      },
      RightM: {
        paddingRight: 10,
      },
      RightML: {
        paddingRight: 20,
      },
      RightL: {
        paddingRight: 30,
      },
      RightXL: {
        paddingRight: 80,
      },
      RightXXL: {
        paddingRight: 160,
      },
      LeftXS: {
        paddingLeft: 3,
      },
      LeftS: {
        paddingLeft: 5,
      },
      LeftMS: {
        paddingLeft: 8,
      },
      LeftM: {
        paddingLeft: 10,
      },
      LeftML: {
        paddingLeft: 20,
      },
      LeftL: {
        paddingLeft: 30,
      },
      LeftXL: {
        paddingLeft: 80,
      },
      LeftXXL: {
        paddingLeft: 160,
      },
      VerticalXS: {
        paddingVertical: 3,
      },
      VerticalS: {
        paddingVertical: 5,
      },
      VerticalMS: {
        paddingVertical: 8,
      },
      VerticalM: {
        paddingVertical: 10,
      },
      VerticalML: {
        paddingVertical: 20,
      },
      VerticalL: {
        paddingVertical: 30,
      },
      VerticalXL: {
        paddingVertical: 80,
      },
      VerticalXXL: {
        paddingVertical: 160,
      },
      HorizontalXS: {
        paddingHorizontal: 3,
      },
      HorizontalS: {
        paddingHorizontal: 5,
      },
      HorizontalMS: {
        paddingHorizontal: 8,
      },
      HorizontalM: {
        paddingHorizontal: 10,
      },
      HorizontalML: {
        paddingHorizontal: 20,
      },
      HorizontalL: {
        paddingHorizontal: 30,
      },
      HorizontalXL: {
        paddingHorizontal: 80,
      },
      HorizontalXXL: {
        paddingHorizontal: 160,
      },
      None: {
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 0,
        paddingRight: 0,
      },
    },
    fonts: {
      regular: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
      },
      bold: {
        fontFamily: 'SourceSans3_700Bold',
        fontWeight: 'normal',
      },
      medium: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
      },
      light: {
        fontFamily: 'SourceSans3_300Light',
        fontWeight: 'normal',
      },
      thin: {
        fontFamily: 'SourceSans3_200ExtraLight',
        fontWeight: 'normal',
      },
      regularTitle: {
        fontFamily: 'Poppins_400Regular',
        fontWeight: 'normal',
      },
      mediumTitle: {
        fontFamily: 'Poppins_600SemiBold',
        fontWeight: 'normal',
      },
      boldTitle: {
        fontFamily: 'Poppins_700Bold',
        fontWeight: 'normal',
      },
      lightTitle: {
        fontFamily: 'Poppins_300Light',
        fontWeight: 'normal',
      },
      thinTitle: {
        fontFamily: 'Poppins_200ExtraLight',
        fontWeight: 'normal',
      },
    },
    fontSizes: {
      xxxLarge: {
        fontSize: 48.83,
      },
      xxLarge: {
        fontSize: 39.06,
      },
      xLarge: {
        fontSize: 31.25,
      },
      large: {
        fontSize: 28,
      },
      mediumLarge: {
        fontSize: 25,
      },
      medium: {
        fontSize: 20,
      },
      mediumSmall: {
        fontSize: 18,
      },
      small: {
        fontSize: 16,
      },
      xSmall: {
        fontSize: 14,
      },
      xxSmall: {
        fontSize: 12,
      },
      xxxSmall: {
        fontSize: 10,
      },
    },
    components: {
      pageContainerConstraints: {
        flexDirection: 'row',
        justifyContent: 'center',
      },
      flexAll: {
        flex: 1,
        alignItems: 'stretch',
      },
      pageConstraints: {
        marginRight: 80,
        marginLeft: 80,
        paddingTop: 20,
      },
      globalPageConstraints: {
        maxWidth: 1440,
      },
      compactPageStyle: {
        marginRight: 80,
        marginLeft: 80,
        paddingTop: 20,
        paddingHorizontal: 80,
        flex: 1,
        alignItems: 'stretch',
      },
      panelStyle: {
        borderWidth: 1,
        borderRadius: 8,
        marginHorizontal: 20,
        marginTop: 20,
        paddingVertical: 20,
        paddingHorizontal: 20,
        backgroundColor: '#fff',
        borderColor: '#C2C2C2',
      },
      sectionStyle: {
        paddingLeft: 20,
        paddingRight: 20,
        paddingTop: 20,
        paddingBottom: 10,
        marginBottom: 30,
      },
      minSectionStyle: {
        paddingLeft: 20,
        paddingRight: 20,
        paddingTop: 5,
        paddingBottom: 5,
        marginBottom: 20,
      },
      groupTitleStyle: {
        fontFamily: 'Poppins_400Regular',
        fontWeight: 'normal',
        fontSize: 25,
        marginBottom: 20,
        color: '#3268DC',
      },
      groupStyle: {
        marginBottom: 30,
        paddingBottom: 20,
        marginLeft: 80,
        marginRight: 160,
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderColor: '#dedede',
      },
      groupTitleContentStyle: {
        maxWidth: 360,
        flex: 1,
      },
      infoSectionStyle: {
        borderColor: '#dedede',
        backgroundColor: '#EAEAF5',
        borderTopWidth: 1,
        borderBottomWidth: 1,
      },
      rowStyle: {
        marginVertical: 5,
      },
      elementStyle: {
        margin: 3,
      },
      buttonPrimaryStyle: {
        borderWidth: 1,
        borderRadius: 4,
        minWidth: 16,
        elevation: 0,
        justifyContent: 'center',
        backgroundColor: '#3268DC',
        height: 38,
        maxHeight: 38,
      },
      buttonContentStyle: {
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 0,
        paddingRight: 0,
        height: 38,
        maxHeight: 38,
        marginRight: 16,
      },
      buttonContentNoIconStyle: {
        paddingLeft: 5,
        marginRight: 13,
      },
      buttonPrimaryTextStyle: {
        letterSpacing: 1.5,
        textTransform: 'uppercase',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        marginTop: 0,
        marginBottom: 0,
        marginLeft: 8,
        marginRight: 0,
        color: '#fff',
      },
      buttonSecondaryStyle: {
        borderWidth: 1,
        borderRadius: 4,
        minWidth: 16,
        elevation: 0,
        justifyContent: 'center',
        borderColor: '#C2C2C2',
        backgroundColor: '#fff',
        height: 38,
        maxHeight: 38,
      },
      buttonSecondaryTextStyle: {
        letterSpacing: 1.5,
        textTransform: 'uppercase',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        marginTop: 0,
        marginBottom: 0,
        marginLeft: 8,
        marginRight: 0,
        color: '#3268DC',
      },
      buttonTertiaryStyle: {
        borderWidth: 1,
        borderRadius: 4,
        minWidth: 16,
        elevation: 0,
        justifyContent: 'center',
        borderColor: '#C2C2C2',
        backgroundColor: '#F4F4FA',
        height: 38,
        maxHeight: 38,
      },
      buttonTertiaryTextStyle: {
        letterSpacing: 1.5,
        textTransform: 'uppercase',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        marginTop: 0,
        marginBottom: 0,
        marginLeft: 8,
        marginRight: 0,
        color: '#333',
      },
      buttonCompactStyle: {
        borderWidth: 1,
        borderRadius: 4,
        minWidth: 16,
        elevation: 0,
      },
      buttonCompactTextStyle: {
        letterSpacing: 1.5,
        textTransform: 'uppercase',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        marginVertical: 3,
        marginLeft: 10,
      },
      auxButton1Style: {
        borderWidth: 1,
        borderRadius: 4,
        minWidth: 16,
        elevation: 0,
        backgroundColor: '#fff',
        borderColor: '#333',
      },
      auxButton1TextStyle: {
        letterSpacing: 1.5,
        textTransform: 'uppercase',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        color: '#333',
      },
      auxButton2Style: {
        borderWidth: 1,
        borderRadius: 4,
        minWidth: 16,
        elevation: 0,
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 0,
        paddingRight: 0,
        backgroundColor: '#252525',
      },
      auxButton2TextStyle: {
        letterSpacing: 1.5,
        textTransform: 'uppercase',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        color: '#fff',
      },
      auxButton3Style: {
        borderWidth: 1,
        borderRadius: 4,
        minWidth: 16,
        elevation: 0,
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 0,
        paddingRight: 0,
        backgroundColor: '#EF233C',
      },
      auxButton3TextStyle: {
        letterSpacing: 1.5,
        textTransform: 'uppercase',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        color: '#fff',
      },
      fabBarStyle: {
        position: 'absolute',
        bottom: 30,
        right: 20,
      },
      systemMessageContainerStyle: {
        padding: 10,
        borderWidth: 1,
        borderRadius: 5,
        position: 'absolute',
        bottom: 30,
        right: 30,
        maxWidth: 350,
        minWidth: 250,
        minHeight: 50,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignContent: 'center',
      },

      textInputStyle: {
        paddingVertical: 8,
        paddingHorizontal: 3,
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        width: '100%',
        borderWidth: 0,
        borderRadius: 5,
        color: '#333',
        outlineColor: '#999',
        minHeight: 38,
      },
      textInputReadOnlyStyle: {
        paddingVertical: 8,
        paddingHorizontal: 3,
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        borderWidth: 0,
        borderRadius: 5,
        color: '#333',
        outlineColor: '#999',
        width: '100%',
      },
      textInputReadOnlyPlaceholderStyle: {
        color: '#9F9F9F',
      },
      textInputContainerStyle: {
        maxWidth: 680,
        borderWidth: 1,
        borderRadius: 5,
        borderColor: '#C2C2C2',
        backgroundColor: '#fff',
      },
      textInputReadOnlyContainerStyle: {
        maxWidth: 680,
        borderWidth: 1,
        borderRadius: 5,
        borderColor: 'rgba(255, 255, 255, 0)',
      },
      textInputIconStyle: {
        paddingVertical: 3,
        marginRight: 3,
        color: '#333',
      },
      searchInputContainerStyle: {
        borderWidth: 1,
        borderRadius: 5,
        borderColor: '#C2C2C2',
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxWidth: 680,
      },
      searchInputSearchBarStyle: {
        elevation: 0,
        flex: 1,
        borderWidth: 0,
        borderRadius: 5,
        height: 38,
        maxHeight: 38,
        backgroundColor: '#fff',
      },
      searchInputTextStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        outlineColor: '#999',
        height: 38,
        maxHeight: 38,
        minHeight: 38,
      },
      searchInputButtonStyle: {
        borderTopLeftRadius: 0,
        borderBottomLeftRadius: 0,
        borderRightWidth: 0,
        borderTopWidth: 0,
        borderBottomWidth: 0,
        height: 38,
        maxHeight: 38,
      },
      dateInputStyle: {
        backgroundColor: '#fff',
        margin: 0,
        padding: 0,
        outlineColor: '#999',
      },
      dateInputContainerStyle: {
        borderWidth: 1,
        borderRadius: 5,
        borderColor: '#C2C2C2',
      },
      labelStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 18,
        color: '#333',
      },
      titleStyle: {
        fontFamily: 'Poppins_400Regular',
        fontWeight: 'normal',
        fontSize: 25,
        color: '#333',
      },
      helperTextInfoStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 14,
        color: '#333',
      },
      helperTextErrorStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 14,
        color: '#CB4154',
      },
      messageInfoStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        color: '#333',
      },
      messageErrorStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        color: '#CB4154',
      },
      dialogStyle: {
        backgroundColor: '#fff',
        alignSelf: 'center',
        borderRadius: 5,
      },
      menuStyle: {
        paddingTop: 10,
        paddingLeft: 10,
        minWidth: 300,
        elevation: 0,
        backgroundColor: '#FFFFFF',
      },
      menuItemStyle: {},
      menuTextStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        color: '#333',
      },
      labeledInputStyle: {
        flexDirection: 'column',
      },
      labeledValueStyle: {
        flexDirection: 'row',
      },
      dropDownMenuAnchorStyle: {
        borderWidth: 1,
        borderRadius: 5,
        minWidth: 16,
        elevation: 0,
        paddingHorizontal: 8,
        borderColor: '#C2C2C2',
        backgroundColor: '#fff',
        maxWidth: 680,
        height: 38,
        maxHeight: 38,
      },
      dropDownMenuReadOnlyAnchorStyle: {
        padding: 3,
        maxWidth: 400,
        height: 38,
        maxHeight: 38,
      },
      dropDownMenuAnchorTextStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        flexWrap: 'nowrap',
      },
      hrStyle: {
        height: 1,
        borderTopWidth: 1,
        borderRightWidth: 0,
        borderLeftWidth: 0,
        borderBottomWidth: 0,
        borderColor: '#dedede',
        borderStyle: 'solid',
      },
      vrStyle: {
        width: 1,
        borderTopWidth: 0,
        borderRightWidth: 0,
        borderLeftWidth: 1,
        borderBottomWidth: 0,
        borderColor: '#dedede',
        borderStyle: 'solid',
      },
      circle: {
        borderRadius: 25,
        borderColor: '#C2C2C2',
        backgroundColor: '#333',
      },
      tag: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingLeft: 10,
        paddingRight: 5,
        paddingVertical: 5,
        borderWidth: 1,
      },
      tagText: {
        fontSize: 16,
        marginRight: 3,
      },
      fileAttachmentStyle: {
        borderWidth: 1,
        borderRadius: 5,
        borderColor: '#C2C2C2',
        padding: 5,
        paddingRight: 80,
        backgroundColor: '#fff',
      },
      fileAttachmentButtonStyle: {
        backgroundColor: '#648DE52F',
        borderWidth: 0,
        borderRadius: 5,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 30,
        paddingHorizontal: 80,
      },
      dataTableStyle: {
        borderBottomWidth: 1,
        borderColor: '#C2C2C2',
        flex: 1,
        alignItems: 'stretch',
      },
      dataTableRowStyle: {
        flexDirection: 'row',
        borderLeftWidth: 0,
        borderRightWidth: 0,
      },
      dataTableCellStyle: {
        justifyContent: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 5,
        paddingVertical: 20,
        borderRightWidth: 1,
        borderColor: '#C2C2C2',
        overflow: 'hidden',
      },
      dataTableHeaderCellStyle: {
        justifyContent: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 5,
        paddingVertical: 10,
        borderColor: 'rgba(255, 255, 255, 1)',
        borderRightWidth: 0,
        backgroundColor: '#fff',
        lineHeight: 22,
      },
      dataTableHeaderTextStyle: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 14,
        textAlign: 'center',
        lineHeight: 15,
        letterSpacing: 0,
        color: '#333',
      },
      dataTableHeaderRowStyle: {
        borderBottomWidth: 2,
        borderColor: '#757575',
      },
      iconContainerStyle: {
        borderRadius: 5,
      },
      iconContainerInvertedStyle: {
        borderRadius: 5,
        padding: 5,
      },
      iconStyle: {},
      linkedItemStyle: {
        borderWidth: 1,
        borderRadius: 5,
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        borderColor: '#C2C2C2',
        padding: 5,
        backgroundColor: '#fff',
        maxWidth: 680,
      },
      linkedItemCompactStyle: {
        borderWidth: 1,
        borderRadius: 5,
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        borderColor: '#C2C2C2',
        padding: 5,
        backgroundColor: '#fff',
        maxWidth: 400,
      },
      linkedItemIconStyle: {
        borderWidth: 1,
        borderRadius: 5,
        borderColor: '#C2C2C2',
        padding: 5,
      },
      linkedItemTextStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        marginRight: 20,
      },
      clickableTextStyle: {
        color: '#3268DC',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 18,
        letterSpacing: 1.2,
      },
      smallClickableTextStyle: {
        color: '#3268DC',
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: 'normal',
        fontSize: 12,
        letterSpacing: 0.2,
      },
      textStyle: {
        color: '#333',
        lineHeight: 25,
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
      },
      inputSetStyle: {
        maxWidth: 680,
      },
      inputSetItemStyle: {
        margin: 5,
        paddingTop: 10,
        paddingLeft: 20,
        paddingBottom: 10,
        paddingRight: 5,
        borderWidth: 1,
        borderRadius: 5,
        borderColor: '#C2C2C2',
        backgroundColor: '#fff',
      },
      tabPanelStyle: {
        flexDirection: 'column',
        alignItems: 'stretch',
        flex: 1,
        borderWidth: 1,
        borderRadius: 8,
        borderColor: '#C2C2C2',
        backgroundColor: '#fff',
      },
      reorderControlsStyle: {
        fontFamily: 'SourceSans3_400Regular',
        fontWeight: 'normal',
        fontSize: 16,
        color: '#757575',
      },
      messageDialogStyle: {
        width: 620,
        height: 550,
      },
      relationshipsDialogStyle: {
        width: 485,
        height: 550,
      },
      upperCase: {
        textTransform: 'uppercase',
      },
      shadow: {
        shadowColor: '#aaa',
        shadowOffset: {
          width: 1,
          height: 1,
        },
        shadowRadius: 5,
        shadowOpacity: 0.5,
      },
    },
  },
};
