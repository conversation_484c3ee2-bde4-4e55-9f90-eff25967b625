import React from 'react';
import { withTheme } from 'react-native-paper';
import { Text } from '../../lib/ui/atoms/Text';
import { View } from 'react-native';
import { v4 } from 'uuid';
import {
  NavigationDrawerMenu,
  NavigationDrawerMenuGroup,
  NavigationDrawerMenuItem,
} from '../../constants/DrawerNavigationLayout';
import { DrawerNavigationItem } from './DrawerNavigationItem';
import { RoleNames } from '../../services/codegen/types';

type DrawerNavigationGroupsProps = {
  theme: ReactNativePaper.ThemeProp;
  onItemSelected: Function;
  isCollapsed: boolean;
  navigationDrawerLayout: NavigationDrawerMenu;
  userRoles: Array<RoleNames>;
  currentRouteName: string;
};

export const DrawerNavigationGroups = withTheme((props: DrawerNavigationGroupsProps) => {
  const { isCollapsed, navigationDrawerLayout, userRoles, currentRouteName, theme } = props;
  const {} = userRoles;
  const {
    styles: { components },
  } = theme;
  function hasPermission(item: NavigationDrawerMenuGroup | NavigationDrawerMenuItem, userRoles: Array<RoleNames>) {
    return item.roles.some((role: any) => userRoles.includes(role));
  }

  return (
    <>
      {navigationDrawerLayout.menu.map((menuGroup: NavigationDrawerMenuGroup) => {
        if (!hasPermission(menuGroup, userRoles)) return null;
        return (
          <View style={[{ gap: 8 }]} key={`${v4()}-${menuGroup.name}`}>
            {!isCollapsed && menuGroup.displayLabel && (
              <View
                style={[
                  components.pageContainerConstraints,
                  {
                    alignItems: 'center',
                    gap: 8,
                  },
                ]}
              >
                <Text style={[props.theme.fonts.regularTitle, { fontSize: 12, color: '#676D79' }]}>
                  {menuGroup.name}
                </Text>
                <View style={{ backgroundColor: '#C4C7CF', height: 1, width: 'auto', flexGrow: 1 }} />
              </View>
            )}
            {menuGroup.items.map((item: NavigationDrawerMenuItem) => {
              if (!hasPermission(item, userRoles)) return null;
              const isActive = currentRouteName === item.name;
              return <DrawerNavigationItem {...props} key={`${v4()}-${item.name}`} isActive={isActive} item={item} />;
            })}
            {isCollapsed && (
              <View
                style={[
                  components.pageContainerConstraints,
                  {
                    alignItems: 'center',
                    backgroundColor: '#C4C7CF',
                    height: 1,
                  },
                ]}
              />
            )}
          </View>
        );
      })}
    </>
  );
});
