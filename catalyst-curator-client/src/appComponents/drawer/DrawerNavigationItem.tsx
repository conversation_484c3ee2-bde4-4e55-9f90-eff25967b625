import React from 'react';
import { withTheme } from 'react-native-paper';
import { NavigationDrawerMenuItem } from '../../constants/DrawerNavigationLayout';
import { Pressable, StyleProp, TextStyle, ViewStyle } from 'react-native';
import Icons from '@expo/vector-icons/MaterialIcons';
import { Label } from '../../lib/ui';
import { useLocalObservable } from 'mobx-react';
import { observer } from 'mobx-react-lite';

type DrawerNavigationItemProps = {
  key?: string,
  item: NavigationDrawerMenuItem;
  theme: ReactNativePaper.ThemeProp;
  onItemSelected: Function;
  isCollapsed: boolean;
  isActive?: boolean;
};

export const DrawerNavigationItem = withTheme(
  observer(({ item, isCollapsed, onItemSelected, theme, isActive, key }: DrawerNavigationItemProps) => {
    const { isHovered, setIsHovered } = useLocalObservable(() => ({
      isHovered: false,
      setIsHovered(value: boolean) {
        this.isHovered = value;
      },
    }));

    const {
      colors,
      styles: { components },
    } = theme;

    return (
      <Pressable
        key={key}
        style={[
          components.pageContainerConstraints,
          {
            gap: 8,
            borderRadius: 4,
            paddingHorizontal: 8,
            paddingVertical: 4,
            alignItems: 'center',
          },
          isCollapsed ? { justifyContent: 'center' } : { justifyContent: 'flex-start' },
          isActive ? { backgroundColor: colors.accent } : {},
          isHovered ? { backgroundColor: '#DCDFEC' } : {},
        ]}
        onPress={() => onItemSelected(item)}
        onHoverIn={() => setIsHovered(true)}
        onHoverOut={() => setIsHovered(false)}
      >
        {!isCollapsed && (
          <>
            <Icon theme={theme} name={item.icon} iconStyle={{ color: '#676D79' }} />
            <Label theme={theme}>{item.label}</Label>
          </>
        )}
        {isCollapsed && <Icon theme={theme} name={item.icon} iconStyle={{ color: '#676D79' }} />}
      </Pressable>
    );
  }),
);

DrawerNavigationItem.defaultProps = {
  onItemSelected: () => {},
  isCollapsed: true,
};

export interface IconProps {
  name: string;
  size?: number;
  color?: string;
  style?: StyleProp<ViewStyle>;
  iconStyle?: StyleProp<TextStyle>;
  inverted?: boolean;
  theme: ReactNativePaper.ThemeProp;
}

export const Icon = withTheme(({ name, size, color, style, iconStyle, inverted, theme }: IconProps) => {
  const {
    styles: { components, defaultValues: defaults },
    colors,
  } = theme;
  const iconColor = inverted ? colors.surface : color;
  return (
    <Icons
      name={name as never}
      color={iconColor}
      style={[components.iconStyle, iconStyle]}
      size={size || defaults.smallIconButtonSize}
    />
  );
});
