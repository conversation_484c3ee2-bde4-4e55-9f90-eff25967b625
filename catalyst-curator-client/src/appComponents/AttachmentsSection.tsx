import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../lib/ui/atoms/Group';
import { Label } from '../lib';
import { AttachmentInfo, FileAttachments } from '../lib/ui/molecules/FileAttachments';
import { UserStore } from '../stores';
import { AttachmentDialog } from '../pages/sections/opportunity/AttachmentDialog';

export type AttachmentLocals = {
  displayName: string;
  notes: string;
  fileName: string;
  mimetype?: string;
  isDialogOpen: boolean;
  editingAttachmentId: string | null;
  setDisplayName(displayName: string): void;
  setNotes(notes: string): void;
  setFileName(fileName: string): void;
  setMimetype(mimetype: string): void;
  setIsDialogOpen(value: boolean): void;
  setEditingAttachmentId(id: string | null): void;
  setPendingFile(file: File | null): void;
  setPendingUri(uri: string): void;
  pendingFile: File | null;
  pendingUri: string;
};

interface AttachmentsSectionProps {
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  title?: string;
  description?: string;
  style?: any;
  editable?: boolean;

  // Attachment data and handlers
  attachments: AttachmentInfo[];
  isUploadInProgress: boolean;
  onRetrieveAttachment: (attachmentId: string) => void;
  onDeleteAttachment: (attachmentId: string) => void;
  onAddAttachment: (result: { file: File; uri: string; displayName?: string; notes?: string }) => Promise<void>;
  onUpdateAttachment: (attachmentId: string, displayName?: string, notes?: string) => Promise<void>;
}

export const AttachmentsSection = withTheme(
  observer(
    ({
      theme,
      userStore,
      editable = true,
      attachments,
      isUploadInProgress,
      onRetrieveAttachment,
      onDeleteAttachment,
      onAddAttachment,
      onUpdateAttachment,
    }: AttachmentsSectionProps) => {
      const {
        colors,
        styles: { margins },
      } = theme;

      const locals = useLocalObservable<AttachmentLocals>(() => ({
        displayName: '',
        notes: '',
        fileName: '',
        mimetype: '',
        isDialogOpen: false,
        editingAttachmentId: null,
        pendingFile: null,
        pendingUri: '',
        setDisplayName(displayName: string) {
          this.displayName = displayName;
        },
        setNotes(notes: string) {
          this.notes = notes;
        },
        setFileName(fileName: string) {
          this.fileName = fileName;
        },
        setMimetype(mimetype: string) {
          this.mimetype = mimetype;
        },
        setIsDialogOpen(value: boolean) {
          this.isDialogOpen = value;
        },
        setEditingAttachmentId(id: string | null) {
          this.editingAttachmentId = id;
        },
        setPendingFile(file: File | null) {
          this.pendingFile = file;
        },
        setPendingUri(uri: string) {
          this.pendingUri = uri;
        },
      }));

      function resetLocals() {
        locals.setIsDialogOpen(false);
        locals.setEditingAttachmentId(null);
        locals.setDisplayName('');
        locals.setNotes('');
        locals.setFileName('');
        locals.setMimetype('');
        locals.setPendingFile(null);
        locals.setPendingUri('');
      }

      return (
        <>
          <Label textStyle={{ color: colors.secondaryTextColor }}>Files</Label>
          <FileAttachments
            style={[margins.BottomL, { marginTop: 8 }]}
            getAttachments={() => attachments}
            onPress={onRetrieveAttachment}
            onDelete={onDeleteAttachment}
            getIsInProgress={() => isUploadInProgress}
            onAddAttachment={async (result) => await onAddAttachment(result)}
            onEditAttachment={(attachmentId) => {
              const attachment = attachments.find((a: AttachmentInfo) => a.id === attachmentId);
              if (attachment) {
                locals.setFileName(attachment.name);
                locals.setDisplayName(attachment.displayName || '');
                locals.setNotes(attachment.notes || '');
                locals.setMimetype(attachment.mimetype || '');
                locals.setEditingAttachmentId(attachmentId);
                locals.setIsDialogOpen(true);
              }
            }}
            onShowAttachmentDialog={(file, uri) => {
              locals.setPendingFile(file);
              locals.setPendingUri(uri);
              locals.setFileName(file.name);
              locals.setMimetype(file.type);
              locals.setDisplayName('');
              locals.setNotes('');
              locals.setEditingAttachmentId(null);
              locals.setIsDialogOpen(true);
            }}
            getEditable={() => editable}
            iconSize={32}
          />
          <AttachmentDialog
            getVisible={() => locals.isDialogOpen}
            onConfirm={async () => {
              if (locals.editingAttachmentId) {
                await onUpdateAttachment(
                  locals.editingAttachmentId,
                  locals.displayName || undefined,
                  locals.notes || undefined,
                );
              } else if (locals.pendingFile && locals.pendingUri) {
                await onAddAttachment({
                  file: locals.pendingFile,
                  uri: locals.pendingUri,
                  displayName: locals.displayName || undefined,
                  notes: locals.notes || undefined,
                });
              }
              resetLocals();
            }}
            onDelete={async () => {
              if (locals.editingAttachmentId) {
                onDeleteAttachment(locals.editingAttachmentId);
                resetLocals();
              }
            }}
            onDismiss={() => {
              resetLocals();
            }}
            locals={locals}
            userStore={userStore}
          />
        </>
      );
    },
  ),
);
