import React from 'react';
import { View, StyleSheet } from 'react-native';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Modal } from '../../lib/ui/atoms/Modal';
import { <PERSON><PERSON>, Text } from '../../lib';
import { OpportunityStore } from '../../stores';
import { ActionTypes } from '../../stores/OpportunityStore';
import { StatusDropdown } from '../../pages/sections/opportunity/StatusDropdown';
import { IconButton } from '../../lib/ui/atoms/IconButton';

const ActionNeededDefinition = {
  statusApproved: `This Opportunity previously had the Approved status that has been sunset. Please\r\nupdate the status of the Opportunity to the appropriate value or create a new Project\r\nPrimer to align this Opportunity correctly.`,
  missingPrimer: `The linked Project Primer for this Opportunity has been deleted. Please update the\r\nstatus of the Opportunity to the appropriate value or create a new Project Primer to\r\nalign this Opportunity correctly.`,
};

interface ModalActionNeededProps {
  style?: object;
  isVisible: boolean;
  opportunityStore: OpportunityStore;
  onDismiss?: () => void;
  onDone?: () => void;
  onCreateProjectPrimer: () => void;
  theme: ReactNativePaper.ThemeProp;
}

export const ModalActionNeeded = withTheme(
  observer((props: ModalActionNeededProps) => {
    const { isVisible, onCreateProjectPrimer, opportunityStore, style, theme } = props;
    const { colors, fonts } = theme;
    const { needsActionOn } = opportunityStore;
    const dialogContent = needsActionOn.includes(ActionTypes.Status)
      ? ActionNeededDefinition.statusApproved
      : ActionNeededDefinition.missingPrimer;

    const { showStatusUpdateSection, showInitialState, toggleShowStatusUpdateSection, resetState } = useLocalObservable(
      () => ({
        showStatusUpdateSection: false,
        showInitialState: true,
        toggleShowStatusUpdateSection() {
          this.showInitialState = !this.showInitialState;
          this.showStatusUpdateSection = !this.showStatusUpdateSection;
        },
        resetState() {
          this.showStatusUpdateSection = false;
          this.showInitialState = true;
        },
      }),
    );

    const handleDebounce = (): void => {
      const { opportunityStore, onDone } = props;
      opportunityStore.saveOpportunity();
      onDone && onDone();
      resetState();
    };

    const handleOnDismiss = (): void => {
      const { onDismiss } = props;
      onDismiss && onDismiss();
      resetState();
    };

    const renderStatusUpdateSection = () => {
      return (
        <View style={{ flexDirection: 'column', flexGrow: 1 }}>
          <StatusDropdown editable={true} opportunityStore={opportunityStore} />
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              style={styles.buttonStyle}
              labelStyle={[styles.buttonLabelStyle, styles.returnLabelStyle]}
              onPress={toggleShowStatusUpdateSection}
              mode="text"
            >
              {'< RETURN'}
            </Button>
            <Button
              style={styles.buttonStyle}
              labelStyle={styles.buttonLabelStyle}
              iconName="view-grid-plus"
              onPress={handleDebounce}
              type="primary"
            >
              {'CONFIRM'}
            </Button>
          </View>
        </View>
      );
    };

    const renderInitialState = () => {
      return (
        <>
          <Button
            style={[styles.buttonStyle, { borderColor: colors.primary }]}
            labelStyle={styles.buttonLabelStyle}
            onPress={toggleShowStatusUpdateSection}
            type="secondary"
          >
            {'STATUS UPDATE'}
          </Button>
          <Button
            style={[styles.buttonStyle, { borderColor: colors.primary }]}
            labelStyle={styles.buttonLabelStyle}
            iconName="view-grid-plus"
            onPress={onCreateProjectPrimer}
            type="secondary"
          >
            {'CREATE PROJECT PRIMER'}
          </Button>
        </>
      );
    };

    return (
      <Modal
        style={[styles.modalStyle, { backgroundColor: colors.surface }, style]}
        getIsVisible={() => !!isVisible}
        onDismiss={handleOnDismiss}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
          <IconButton
            style={styles.buttonCloseStyle}
            color={colors.text}
            icon="close"
            size={20}
            onPress={handleOnDismiss}
          />
        </View>
        <View style={styles.informationRow}>
          <Text style={[styles.textHeader, fonts.medium]}>{'Action Needed'}</Text>
          <Text style={styles.textBody}>{dialogContent}</Text>
        </View>
        <View style={styles.buttonRow}>
          {showInitialState && renderInitialState()}
          {showStatusUpdateSection && renderStatusUpdateSection()}
        </View>
      </Modal>
    );
  }),
);

ModalActionNeeded.defaultProps = {
  isVisible: false,
  onDismiss: () => {},
  onDone: () => {},
  style: {},
};

const styles = StyleSheet.create({
  textHeader: {
    fontSize: 32,
    fontWeight: 500,
    lineHeight: 40,
    letterSpacing: -0.4,
  },
  textBody: {
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 22,
  },
  informationRow: {
    display: 'flex',
    justifyContent: 'center',
    gap: 8,
    paddingBottom: 32,
    paddingHorizontal: 32,
  },
  buttonRow: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingBottom: 32,
    gap: 16,
  },
  buttonStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
  buttonCloseStyle: {
    padding: 0,
    alignItems: 'flex-end',
  },
  returnLabelStyle: {
    color: '#323438',
  },
  buttonLabelStyle: {
    textTransform: 'capitalize',
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 18,
  },
  modalStyle: { alignSelf: 'center', flexDirection: 'column', padding: 10, borderRadius: 16 },
});
