import { withTheme } from 'react-native-paper';
import { Link } from '../services/codegen/types';
import { UserStore } from '../stores';
import { useLocalObservable } from 'mobx-react';
import { Button, Label, TextInput, urlValidator } from '../lib';
import { Linking, View } from 'react-native';
import { InfoCard } from '../lib/ui/molecules/InfoCard';
import { LinkDialog } from './LinkDialog';

export type LinkLocals = {
  url: string;
  name: string;
  isDialogOpen: boolean;
  notes: string;
  editingLinkId: string | null;
  setUrl(url: string): void;
  setName(name: string): void;
  setNotes(notes: string): void;
  setIsDialogOpen(value: boolean): void;
  setEditingLinkId(id: string | null): void;
  urlErrorMessage: string;
  setUrlErrorMessage(value: string): void;
};
interface LinksSectionProps {
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  editable?: boolean;
  links: Link[];
  onAddLink: (url: string, name: string, notes: string) => void;
  onDeleteLink: (id: string) => void;
  onUpdateLink: (id: string, url: string, name: string, notes: string) => void;
}

export const LinksSection = withTheme(
  ({ theme, userStore, editable = true, links, onAddLink, onDeleteLink, onUpdateLink }: LinksSectionProps) => {
    const { colors } = theme;

    const locals = useLocalObservable<LinkLocals>(() => ({
      url: '',
      name: '',
      notes: '',
      isDialogOpen: false,
      editingLinkId: null,
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      setEditingLinkId(id: string | null) {
        this.editingLinkId = id;
      },
      setUrl(url: string) {
        this.url = url;
      },
      setName(name: string) {
        this.name = name;
      },
      setNotes(notes: string) {
        this.notes = notes;
      },
      urlErrorMessage: '',
      setUrlErrorMessage(value: string) {
        this.urlErrorMessage = value;
      },
    }));

    function resetLocals() {
      locals.setIsDialogOpen(false);
      locals.setEditingLinkId(null);
      locals.setUrl('');
      locals.setName('');
      locals.setNotes('');
      locals.setUrlErrorMessage('');
    }
    return (
      <>
        <Label textStyle={{ color: colors.secondaryTextColor, marginBottom: 8 }}>Links</Label>
        <View style={{ marginBottom: 32 }}>
          {editable ? (
            <View>
              <Label textStyle={{ color: colors.disclaimerSecondary }}>Destination URL</Label>
              <View style={{ flexDirection: 'row', gap: 4 }}>
                <TextInput
                  getValue={() => locals.url}
                  setValue={(value) => locals.setUrl(value)}
                  containerStyle={{ backgroundColor: colors.background, flex: 1 }}
                  placeholder="https://example.com"
                  iconName="link"
                  multiline={false}
                />
                <Button
                  type="primary"
                  onPress={() => locals.setIsDialogOpen(true)}
                  labelStyle={{ textTransform: 'capitalize' }}
                  getDisabled={() => !locals.url}
                >
                  Add Link
                </Button>
              </View>
            </View>
          ) : null}
          <View style={{ gap: 8, marginTop: 16 }}>
            {links?.map((link) => {
              return (
                <InfoCard
                  key={link.id}
                  id={link.id}
                  title={link.name || link.url}
                  subtitle={link.name ? link.url : undefined}
                  iconName="link"
                  iconColor={colors.secondary}
                  notes={link.notes || undefined}
                  onPress={() => {
                    Linking.openURL(link.url);
                  }}
                  onEdit={
                    editable
                      ? () => {
                          locals.setUrl(link.url);
                          locals.setName(link.name);
                          locals.setNotes(link.notes || '');
                          locals.setEditingLinkId(link.id);
                          locals.setIsDialogOpen(true);
                        }
                      : undefined
                  }
                  theme={theme}
                  createdBy={link.createdBy || undefined}
                  createdAt={link.createdAt}
                />
              );
            })}
          </View>
        </View>
        <LinkDialog
          getVisible={() => locals.isDialogOpen}
          dismissable={true}
          onConfirm={async () => {
            if (!locals.url) {
              locals.setUrlErrorMessage('URL is required');
              return;
            }
            if (!urlValidator(locals.url)) {
              locals.setUrlErrorMessage('Invalid URL');
              return;
            }

            if (locals.editingLinkId) {
              onUpdateLink(locals.editingLinkId, locals.url, locals.name, locals.notes);
            } else {
              onAddLink(locals.url, locals.name, locals.notes);
            }
            resetLocals();
          }}
          onDelete={async () => {
            if (locals.editingLinkId) {
              onDeleteLink(locals.editingLinkId);
              resetLocals();
            }
          }}
          onDismiss={() => {
            resetLocals();
          }}
          locals={locals}
          userStore={userStore}
        />
      </>
    );
  },
);
