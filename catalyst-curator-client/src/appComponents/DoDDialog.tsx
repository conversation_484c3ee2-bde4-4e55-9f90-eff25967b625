import { observer, useLocalObservable } from 'mobx-react';
import { MessageDialog } from '../lib/ui/molecules/MessageDialog';
import { TenantStore } from '../stores';

type DoDDialogProps = {
  tenantStore: TenantStore;
};
export const DoDDialog = observer(({ tenantStore }: DoDDialogProps) => {
  const localStore = useLocalObservable(() => ({
    showDialog: true,
    setShowDialog(value: boolean) {
      this.showDialog = value;
    },
  }));

  const isBannerEnabled = tenantStore.tenantContent?.featureFlags.dodBanner.enabled || false;
  return (
    <MessageDialog
      getVisible={() => {
        return localStore.showDialog && isBannerEnabled
        // return localStore.showDialog;
      }}
      onConfirm={() => {
        localStore.setShowDialog(false);
      }}
      confirmLabel="Confirm"
      message={DIALOG_TEXT}
      title={'Standard Mandatory DoD Notice and Consent'}
    />
  );
});

const DIALOG_TEXT =
  'You are accessing a U.S. Government (USG) Information System (IS) that is provided for USG-authorized use only.\n\n' +
  'By using this IS (which includes any device attached to this IS), you consent to the following conditions:\n\n' +
  '- The USG routinely intercepts and monitors communications on this IS for purposes including, but not limited to, penetration testing, COMSEC monitoring, network operations and defense, personnel misconduct (PM), law enforcement (LE), and counterintelligence (CI) investigations.\n\n' +
  '- At any time, the USG may inspect and seize data stored on this IS.\n\n' +
  '- Communications using, or data stored on, this IS are not private, are subject to routine monitoring, interception, and search, and may be disclosed or used for any USG-authorized purpose.\n\n' +
  '- This IS includes security measures (e.g., authentication and access controls) to protect USG interests--not for your personal benefit or privacy.\n\n' +
  '- Notwithstanding the above, using this IS does not constitute consent to PM, LE or CI investigative searching or monitoring of the content of privileged communications, or work product, related to personal representation or services by attorneys, psychotherapists, or clergy, and their assistants. Such communications and work product are private and confidential. See User Agreement for details.';
