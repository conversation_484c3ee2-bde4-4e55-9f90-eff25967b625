import React from 'react';
import { View, StyleSheet } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Modal } from '../lib/ui/atoms/Modal';
import { Button, Text } from '../lib';

interface ModalArchiveOpportunityProps {
  isVisible?: boolean;
  onDismiss?: () => void;
  onDone?: () => void;
  theme: ReactNativePaper.ThemeProp;
  style?: object;
}

export const ModalArchiveOpportunity = withTheme((props: ModalArchiveOpportunityProps) => {
  const { isVisible, onDismiss, onDone, style, theme } = props;
  const { colors, fonts } = theme;
  return (
    <Modal
      style={[styles.modalStyle, { backgroundColor: colors.surface }, style]}
      getIsVisible={() => !!isVisible}
      onDismiss={onDismiss}
    >
      <View style={{ display: 'flex', flexDirection: 'column' }}>
        <Text theme={theme} style={[styles.textHeader, fonts.medium]}>
          {'Archive Opportunity'}
        </Text>
        <Text theme={theme} style={styles.textBody}>
          {
            'Archiving this Opportunity will hide it from the Opportunity table\r\nview. You can toggle on all Archived Opportunities in the Filters\r\nlocated above the table.'
          }
        </Text>
        <View style={styles.buttonRow}>
          <Button
            style={styles.buttonStyle}
            labelStyle={[styles.buttonLabelStyle, styles.cancelLabelStyle]}
            onPress={onDismiss}
            type="secondary"
          >
            {'Cancel'}
          </Button>
          <Button
            style={[styles.buttonStyle, { backgroundColor: colors.negativeColor }]}
            labelStyle={styles.buttonLabelStyle}
            onPress={onDone}
            type="primary"
          >
            {'Archive'}
          </Button>
        </View>
      </View>
    </Modal>
  );
});

ModalArchiveOpportunity.defaultProps = {
  isVisible: false,
  onDismiss: () => {},
  onDone: () => {},
  style: {},
};

const styles = StyleSheet.create({
  textHeader: {
    fontSize: 32,
    paddingHorizontal: 32,
    paddingTop: 44,
    paddingBottom: 36,
    gap: 10,
  },
  textBody: {
    paddingHorizontal: 32,
    paddingTop: 16,
  },
  buttonRow: {
    display: 'flex',
    flexDirection: 'row',
    paddingVertical: 40,
    paddingHorizontal: 32,
    justifyContent: 'flex-end',
    gap: 8,
  },
  buttonStyle: {
    justifyContent: 'center',
    borderRadius: 4,
    borderWidth: 0,
    height: 40,
  },
  cancelLabelStyle: {
    color: '#323438',
  },
  buttonLabelStyle: {
    textTransform: 'capitalize',
  },
  modalStyle: { alignSelf: 'center', borderRadius: 16 },
});
