import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Router } from '../../platform/Router';
import { TenantStore, UserStore } from '../../stores';
import { NavigationDrawerMenuItem, NavigationDrawerMenuLayout } from '../../constants/DrawerNavigationLayout';
import { MainStackParamList } from '../../routing/screens';
import { Pressable, View } from 'react-native';
import { DrawerNavigationHeader } from '../../lib/web/drawer/DrawerNavigationHeader';
import { DrawerNavigationGroups } from '../drawer/DrawerNavigationGroups';
import { PopupListMenu, UserProfileBadge } from '../../lib';
import { Icon } from '../../lib/ui/atoms/Icon';
import { RoleNames } from '../../services/codegen/types';

type WebDrawerProps = {
  router: Router<MainStackParamList>;
  tenantStore: TenantStore;
  userStore: UserStore;
  theme: ReactNativePaper.ThemeProp;
  currentRouteName: string;
};

export const WebDrawer = withTheme(
  observer(({ router, theme, tenantStore, userStore, currentRouteName }: WebDrawerProps) => {
    const isMenuCollapsed = userStore.isMenuCollapsed;
    const { user, isAdminUser, isAnalystUser, isCuratorUser } = userStore;
    const firstName = !user ? '' : userStore.user?.firstName;
    const lastName = !user ? '' : userStore.user?.lastName;
    const tenantLogo = tenantStore.getImageSourceProp(tenantStore.tenantConfig.logo);
    const tenantName = tenantStore.tenantConfig?.applicationTitle || '';
    const userRoles = [];
    if (isAdminUser) userRoles.push(RoleNames.Admin);
    if (isAnalystUser) userRoles.push(RoleNames.Analyst);
    if (isCuratorUser) userRoles.push(RoleNames.Curator);
    const { colors } = theme;
    const localStore = useLocalObservable(() => ({
      setSelectedItem(value: NavigationDrawerMenuItem) {
        (router as any).navigate(value.name, { location: value.location });
      },
    }));

    const imageIconName = isMenuCollapsed ? 'dock-left' : 'dock-left';
    return (
      <View
        style={{
          borderRightWidth: 1,
          borderColor: '#E6E9F0',
          backgroundColor: 'white',
        }}
      >
        <View style={{ flex: 1 }}>
          <View
            style={{
              flexDirection: 'column',
              gap: 8,
              flexGrow: 1,
              paddingTop: 16,
              paddingBottom: 8,
              paddingHorizontal: 16,
            }}
          >
            <DrawerNavigationHeader
              theme={theme}
              imageSource={tenantLogo}
              label={tenantName}
              isCollapsed={isMenuCollapsed}
            />
            <DrawerNavigationGroups
              theme={theme}
              onItemSelected={localStore.setSelectedItem}
              isCollapsed={isMenuCollapsed}
              navigationDrawerLayout={NavigationDrawerMenuLayout}
              userRoles={userRoles}
              currentRouteName={currentRouteName}
            />
          </View>
        </View>
        <View style={{ flexDirection: 'row', borderTopWidth: 1, borderColor: '#E6E9F0' }}>
          <View
            style={[
              { flexDirection: 'column', flexGrow: 1, paddingBottom: 8, paddingHorizontal: 16, gap: 8 },
              isMenuCollapsed ? { alignItems: 'center' } : {},
            ]}
          >
            <View style={{ flexDirection: 'row', flexShrink: 1, paddingTop: 8 }}>
              <Pressable
                style={[
                  { borderWidth: 1, borderColor: '#C4C7CF', borderRadius: 4 },
                  isMenuCollapsed ? { alignItems: 'center' } : { alignItems: 'flex-start' },
                ]}
                onPress={() => userStore.setIsMenuCollapsed(!isMenuCollapsed)}
              >
                <Icon name={imageIconName} size={24} color="#676D79" />
              </Pressable>
            </View>
            <PopupListMenu
              style={{ maxWidth: 300 }}
              getMenuItems={() => {
                const items = [];
                items.push({ label: 'Profile', value: 'profile' });
                items.push({ label: 'Logout', value: 'logout' });
                return items;
              }}
              onItemSelected={(item: any) => {
                if (item.value === 'profile') {
                  localStore.setSelectedItem({ label: item.label, name: item.value } as NavigationDrawerMenuItem);
                } else if (item.value === 'logout') {
                  userStore.signOut();
                }
              }}
              anchor={
                <UserProfileBadge
                  {...{
                    firstName: firstName,
                    lastName: lastName,
                    style: { margin: 0, marginHorizontal: 0 },
                    avatarStyle: {
                      backgroundColor: colors.mediumGray,
                      margin: 0,
                      marginHorizontal: 4,
                      height: 24,
                      width: 24,
                    },
                    textStyle: [theme.fontSizes.xxxSmall, theme.fonts.regularTitle, { color: colors.text }],
                    avatarTextSize: 26,
                    isCollapsed: isMenuCollapsed,
                  }}
                />
              }
            />
          </View>
        </View>
      </View>
    );
  }),
);
