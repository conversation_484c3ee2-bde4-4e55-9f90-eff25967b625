import { observer, useLocalObservable } from 'mobx-react';
import React, { useRef, useState } from 'react';
import { Menu, withTheme } from 'react-native-paper';
import Icons from '@expo/vector-icons/MaterialCommunityIcons';
import { MenuItem } from '../atoms/PopupListMenu';
import { Pressable, ScrollView, StyleProp, View, ViewStyle } from 'react-native';
import { Text } from '../atoms';
import { observable, runInAction, toJS } from 'mobx';

export type MenuGroup = { item: MenuItem; children?: MenuGroup[] };

export type MenuItemFieldValue = {
  value?: MenuItem;
  fieldName: string;
  label?: string;
};

type Locals = {
  isMenuOpen: boolean;
  currentMenuLevel: number;
  selectedValues: (MenuGroup | undefined)[];
  setIsMenuOpen(value: boolean): void;
  setSelectedValueAtLevel(index: number, item: MenuGroup | undefined): void;
  setCurrentMenuLevel(value: number): void;
};

export interface DependentDropdownProps {
  getMenuGroups: () => MenuGroup[];
  getEditable?: () => boolean;
  onItemSelected: (item: MenuItem | undefined, fieldName: string) => void;
  defaultValues: MenuItemFieldValue[];
  menuStyle?: StyleProp<ViewStyle>;
  maxHeight?: number;
  anchorStyle?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
}

export const DependentDropdown = withTheme(
  observer(
    ({
      defaultValues,
      getEditable = () => true,
      getMenuGroups,
      onItemSelected,
      maxHeight = 300,
      menuStyle,
      anchorStyle,
      theme,
    }: DependentDropdownProps) => {
      const {
        colors,
        styles: { components },
      } = theme;

      const editable = getEditable();
      const anchorRef = useRef<View>(null);
      const [anchorWidth, setAnchorWidth] = useState<number>(0);

      const localStore = useLocalObservable<Locals>(() => {
        const initialPath: (MenuGroup | undefined)[] = [];
        let children = getMenuGroups();

        if (defaultValues?.length) {
          for (const def of defaultValues) {
            const found = children.find((g) => g.item.value === def.value?.value);
            if (!found) break;
            initialPath.push(found);
            children = found.children ?? [];
          }
        }

        return {
          isMenuOpen: false,
          currentMenuLevel: 0,
          selectedValues: observable.array<MenuGroup | undefined>(initialPath),

          setIsMenuOpen(value: boolean) {
            this.isMenuOpen = value;
          },

          setSelectedValueAtLevel(index: number, item: MenuGroup | undefined) {
            this.selectedValues[index] = item;
            this.selectedValues.splice(index + 1);
            this.currentMenuLevel = index + 1;
          },

          setCurrentMenuLevel(value: number) {
            this.currentMenuLevel = value;
          },
        };
      });

      function getCurrentGroups() {
        let groups = getMenuGroups();

        for (let i = 0; i < currentLevel; i++) {
          const selected = localStore.selectedValues[i];
          if (!selected) break;

          const found = groups.find((g) => g.item.value === selected.item.value);
          if (!found) break;
          const children = found.children;
          if (!children) break;
          groups = children;
        }

        return groups;
      }

      function getSelectedItemAtLevel(level: number): MenuGroup | undefined {
        return localStore.selectedValues[level];
      }

      const currentLevel = localStore.currentMenuLevel;
      const selectedItem = getSelectedItemAtLevel(currentLevel);
      const previousItem = localStore.selectedValues[currentLevel - 1];

      if (!editable) return <Text>{localStore.selectedValues.map((s) => s?.item.label).join(' > ')}</Text>;

      return (
        <Menu
          visible={localStore.isMenuOpen}
          onDismiss={() => localStore.setIsMenuOpen(false)}
          anchorPosition="bottom"
          contentStyle={[
            {
              maxHeight: maxHeight,
              width: anchorWidth,
              maxWidth: anchorWidth,
              overflow: 'scroll',
              elevation: 0,
              backgroundColor: theme.colors.light,
            },
            menuStyle,
          ]}
          anchor={
            <MenuAnchor
              localStore={localStore}
              editable={editable}
              anchorRef={anchorRef}
              anchorWidth={anchorWidth}
              setAnchorWidth={setAnchorWidth}
              anchorStyle={anchorStyle}
              theme={theme}
            />
          }
        >
          <ScrollView>
            {currentLevel > 0 && previousItem && (
              <Pressable
                onPress={() => {
                  runInAction(() => {
                    localStore.setCurrentMenuLevel(currentLevel - 1);
                  });
                }}
                style={{
                  paddingVertical: 8,
                  paddingHorizontal: 16,
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderBottomColor: colors.uiBorderColor,
                  borderBottomWidth: 1,
                }}
              >
                <Icons name="chevron-left" size={20} color={colors.textSecondary} />
                <Text>{previousItem.item.label}</Text>
              </Pressable>
            )}
            {getCurrentGroups().map((group) => {
              const isSelected = selectedItem?.item.value === group.item.value;
              return (
                <Pressable
                  style={{
                    paddingVertical: 8,
                    paddingHorizontal: 16,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    backgroundColor: isSelected ? colors.secondaryPrimaryColor : undefined,
                  }}
                  onPress={async () => {
                    const fieldName = defaultValues[currentLevel]?.fieldName;
                    if (isSelected) {
                      runInAction(() => {
                        localStore.currentMenuLevel = currentLevel + 1;
                      });
                    } else {
                      onItemSelected(group.item, fieldName);
                      localStore.setSelectedValueAtLevel(currentLevel, group);
                    }
                    if (!group.children) {
                      runInAction(() => {
                        localStore.setIsMenuOpen(false);
                        localStore.setCurrentMenuLevel(0);
                      });
                    }
                  }}
                  key={group.item.value}
                >
                  <Text>{group.item.label}</Text>
                  {group.children && (
                    <Icons name="chevron-right" size={24} color={colors.secondaryTextColor} style={{ marginLeft: 8 }} />
                  )}
                </Pressable>
              );
            })}
          </ScrollView>
        </Menu>
      );
    },
  ),
);

type MenuAnchorProps = {
  localStore: Locals;
  editable: boolean;
  anchorRef: React.RefObject<View>;
  anchorWidth: number;
  setAnchorWidth: (width: number) => void;
  anchorStyle?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
};
const MenuAnchor = withTheme(
  observer(({ theme, localStore, editable, anchorRef, anchorWidth, setAnchorWidth, anchorStyle }: MenuAnchorProps) => {
    const {
      styles: { components },
      colors,
    } = theme;

    return (
      <Pressable
        onPress={() => {
          if (editable) {
            localStore.setIsMenuOpen(true);
          }
        }}
        disabled={!editable}
        ref={anchorRef}
        onLayout={(event) => {
          setAnchorWidth(event.nativeEvent.layout.width);
        }}
        style={[
          editable ? components.dropDownMenuAnchorStyle : components.dropDownMenuReadOnlyAnchorStyle,
          {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          },
          anchorStyle,
        ]}
      >
        <View style={[{ flex: 1, flexDirection: 'row', alignItems: 'center', gap: 8 }]}>
          <Text style={{ fontSize: 16 }}>
            {localStore.selectedValues.map((s) => s?.item.label).join(' > ') || 'Select...'}
          </Text>
        </View>
        {editable && <Text style={[{ color: colors.border }]}>{'\u25bc'}</Text>}
      </Pressable>
    );
  }),
);
