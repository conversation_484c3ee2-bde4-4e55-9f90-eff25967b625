import React from "react";
import { withTheme } from "react-native-paper";
import { LabeledInput, LabeledInputProps } from "./LabeledInput";
import { DependentDropdown, DependentDropdownProps } from "./DependentDropdown";

export interface LabeledDependentDropdownProps extends LabeledInputProps {
  dropdownMenuProps: DependentDropdownProps;
}

export const LabeledDependentDropdown = withTheme(
  ({
    dropdownMenuProps,
    ...rest
  }: LabeledDependentDropdownProps & { theme: ReactNativePaper.ThemeProp }) => {
    return (
      <LabeledInput {...rest}>
        <DependentDropdown {...dropdownMenuProps} />
      </LabeledInput>
    );
  }
);
