interface OpportunityCampaignReportResult {
  tenantCampaignResults: TenantCampaignResult[];
  totals: TenantCampaignResult[];
}

interface TenantCampaignResult {
  tenant?: string;
  campaign: string;
  campaignCount: number;
}

interface OpportunityPriorityReportResult {
  tenantName: string;
  highPriorityCount: number;
  mediumPriorityCount: number;
  lowPriorityCount: number;
  noPriorityCount: number;
}

interface OpportunityStatusReportResult {
  tenant: string;
  advancedCount: number;
  pendingCount: number;
  archivedCount: number;
  newCount: number;
  inCurationCount: number;
  onHoldCount: number;
  completedCount: number;
}

interface OpportunitySubmissionReportResult {
  totalSubmissions: number;
  totalSubmitters: number;
  uncuratedOpportunities: number;
}

interface TenantSubmissionByYearReportResult {
  tenantSubmissionResults: TenantSubmissionYearResult[];
  totals: TenantSubmissionTotalYearResult[];
}

interface TenantSubmissionYearResult {
  tenant: string;
  year: string;
  submissionCount: number;
}
interface TenantSubmissionTotalYearResult {
  year: string;
  submissionCount: number;
}

interface TenantSubmissionByMonthReportResult {
  tenantSubmissionResults: TenantSubmissionMonthResult[];
  totals: TenantSubmissionTotalMonthResult[];
}

interface TenantSubmissionMonthResult {
  tenant: string;
  month: string;
  submissionCount: number;
}

interface TenantSubmissionTotalMonthResult {
  month: string;
  submissionCount: number;
}

interface OpportunityWFFReportResult {
  tenant: string;
  missionCommandCount: number;
  movementManeuverCount: number;
  intelligenceCount: number;
  firesCount: number;
  sustainmentCount: number;
  forceProtectionCount: number;
  noneCount: number;
}
