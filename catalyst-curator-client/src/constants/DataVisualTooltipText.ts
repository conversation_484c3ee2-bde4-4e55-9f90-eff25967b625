export const DataVisualTooltipText = {
  campaign:
    'This metric tracks specific designated activities, often relevant to multiple projects or across portfolios. Note: If “No event data available” is displayed this tenant(s) do not have any associated Events.',
  wff: 'These metrics represent groups of the six (6) functional areas used by commanders to achieve missions and training objectives.',
  priority: 'This metric categorizes opportunities based on their assigned priority level: High, Medium, Low, or None.',
  status:
    'This metric categorizes opportunities based on their current status: New, In Curation, On-hold, Advanced, Closed, and Archived',
  problemSubmissions: 'This metric represents the total number of submissions received within the selected time frame.',
  uncuratedSubmissions:
    'This metric represents the number of submissions that have not yet been opened, reviewed or updated.',
  submitters: 'This metric indicates the total number of unique users who have made submissions.',
  totalSubmissions: 'This metric represents the total number of unique submissions received.',
};
