import { StackScreenProps } from '@react-navigation/stack';
import React, { Component } from 'react';
import { AppContent } from '../lib';
import { AppFooter } from '../appComponents/AppFooter';
import CurationPage from '../pages/CurationPage';
import { CuratorStores } from '../platform/initializers';
import { PlatformFactory } from '../platform/PlatformFactory';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { MainStackParamList } from '../routing/screens';
import OpportunityStore from '../stores/OpportunityStore';
import { OpportunityFab } from '../pages/sections/opportunity/OpportunityFab';
import { withTheme } from 'react-native-paper';
import { Wait } from '../lib/ui/molecules/Wait';
import { QueryObserver } from '../lib/stores/Store';

import { WebDrawer } from '../appComponents/web/WebDrawer';
import { View } from 'react-native';
import { OpportunityOwnerListStore } from '../stores/OpportunityOwnerListStore';
import { OpportunityStatus } from '../services/codegen/types';

type CurationRouteProps = StackScreenProps<MainStackParamList, 'curation'>;
class CurationRoute extends Component<CurationRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  #unsubscribe?: () => void;

  render() {
    const {
      route: {
        params: { id },
      },
    } = this.props;
    const router = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation);
    const storesProvider = StoresProvider.get<CuratorStores>();
    const opportunityStore = OpportunityStore.getStoreForId(id);
    const opportunityOwnerListStore = new OpportunityOwnerListStore(opportunityStore.opportunityId);

    const handleProjectCreated = (id: string) => {
      router.navigate('project', { id });
      opportunityStore.setValue('status', OpportunityStatus.Advanced);
      opportunityStore.saveOpportunity();
    };

    const {
      styles: { components },
    } = this.props.theme;
    const curationProps = {
      opportunityStore,
      router,
      userStore: storesProvider.getStore('UserStore'),
      categoryStore: storesProvider.getStore('CategoryStore'),
      stakeholderStore: storesProvider.getStore('StakeholderStore'),
      opportunityOwnerStore: storesProvider.getStore('OpportunityOwnerStore'),
      tenantStore: storesProvider.getStore('TenantStore'),
      opportunityOwnerListStore,
      handleProjectCreated,
    };

    const queryObserver = new QueryObserver(
      opportunityStore,
      curationProps.userStore,
      curationProps.categoryStore,
      curationProps.stakeholderStore,
      curationProps.tenantStore,
      curationProps.opportunityOwnerStore,
      opportunityOwnerListStore,
    );

    return (
      <Wait until={() => opportunityStore.initialized}>
        <AppContent
          noScroll={true}
          getFabContent={() => (
            <OpportunityFab
              queryObserver={queryObserver}
              opportunityStore={opportunityStore}
              onProjectCreated={handleProjectCreated}
            />
          )}
        >
          <View style={[components.pageContainerConstraints, components.flexAll]}>
            <WebDrawer {...curationProps} currentRouteName="" />
            <View style={[components.pageContentConstraints, components.flexAll]}>
              <CurationPage {...curationProps} />
              <AppFooter />
            </View>
          </View>
        </AppContent>
      </Wait>
    );
  }

  componentDidMount() {
    this.#unsubscribe = this.props.navigation.addListener('focus', () => {
      const {
        route: {
          params: { id },
        },
      } = this.props;
      const store = OpportunityStore.getStoreForId(id);
      store?.initialize();
    });
  }

  componentWillUnmount() {
    this.#unsubscribe && this.#unsubscribe();
    const {
      route: {
        params: { id },
      },
    } = this.props;
    OpportunityStore.removeStoreForId(id);
    const storesProvider = StoresProvider.get<CuratorStores>();
    storesProvider.getStore('CategoryStore').clearAll();
    storesProvider.getStore('StakeholderStore').clearAll();
  }
}

export default withTheme(CurationRoute);
