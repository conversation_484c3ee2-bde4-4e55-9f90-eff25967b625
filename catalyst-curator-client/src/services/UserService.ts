import {
  AuthResponse,
  CreateUserDocument,
  CreateUserMutation,
  CreateUserMutationVariables,
  DeleteCurrentUserDocument,
  DeleteCurrentUserMutation,
  DeleteCurrentUserMutationVariables,
  DeleteUserDocument,
  DeleteUserMutation,
  DeleteUserMutationVariables,
  GetCurrentUserInformationDocument,
  GetCurrentUserInformationQuery,
  GetCurrentUserInformationQueryVariables,
  GetCurrentUserStatusDocument,
  GetCurrentUserStatusQuery,
  GetCurrentUserStatusQueryVariables,
  GetUserDocument,
  GetUserQuery,
  GetUserQueryVariables,
  MutationCreateUserArgs,
  MutationDeleteUserArgs,
  MutationLoginArgs,
  MutationUpdateCurrentUserArgs,
  MutationUpdateCurrentUserPasswordArgs,
  MutationUpdateUserArgs,
  QueryGetUserArgs,
  QueryQueryUsersArgs,
  QueryUsersDocument,
  QueryUsersQuery,
  QueryUsersQueryVariables,
  RenewTokenDocument,
  RenewTokenMutation,
  RenewTokenMutationVariables,
  UpdateCurrentUserDocument,
  UpdateCurrentUserMutation,
  UpdateCurrentUserMutationVariables,
  UpdateCurrentUserPasswordDocument,
  UpdateCurrentUserPasswordMutation,
  UpdateCurrentUserPasswordMutationVariables,
  UpdateUserDocument,
  UpdateUserMutation,
  UpdateUserMutationVariables,
  User,
  UserLoginDocument,
  UserLoginMutation,
  UserLoginMutationVariables,
  UserPage,
  VerifiedStatus,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class UserService {
  resetCache: boolean = false;

  async loginUser(input: MutationLoginArgs): Promise<AuthResponse> {
    const { data, error } = await urqlClient.executeMutation<UserLoginMutation, UserLoginMutationVariables>(
      UserLoginDocument,
      input,
    );
    if (error) throw error;
    return data!.login as AuthResponse;
  }

  async renewToken(): Promise<AuthResponse> {
    const { data, error } = await urqlClient.executeMutation<RenewTokenMutation, RenewTokenMutationVariables>(
      RenewTokenDocument,
    );
    if (error) throw error;
    return data!.renew as AuthResponse;
  }

  async getCurrentUser(): Promise<User> {
    const { data, error } = await urqlClient.executeQuery<
      GetCurrentUserInformationQuery,
      GetCurrentUserInformationQueryVariables
    >(GetCurrentUserInformationDocument);
    if (error) throw error;
    return data!.getCurrentUser as User;
  }

  async getCurrentUserStatus(): Promise<VerifiedStatus> {
    const { data, error } = await urqlClient.executeQuery<
      GetCurrentUserStatusQuery,
      GetCurrentUserStatusQueryVariables
    >(GetCurrentUserStatusDocument);
    if (error) throw error;
    return data!.getCurrentUser!.status;
  }

  async updateCurrentUser(input: MutationUpdateCurrentUserArgs): Promise<User> {
    const { data, error } = await urqlClient.executeMutation<
      UpdateCurrentUserMutation,
      UpdateCurrentUserMutationVariables
    >(UpdateCurrentUserDocument, input);
    if (error) throw error;
    return data!.updateCurrentUser as User;
  }

  async updateCurrentUserPassword(input: MutationUpdateCurrentUserPasswordArgs): Promise<User> {
    const { data, error } = await urqlClient.executeMutation<
      UpdateCurrentUserPasswordMutation,
      UpdateCurrentUserPasswordMutationVariables
    >(UpdateCurrentUserPasswordDocument, input);
    if (error) throw error;
    return data!.updateCurrentUserPassword as User;
  }

  async deleteCurrentUser(): Promise<boolean> {
    const { data, error } = await urqlClient.executeMutation<
      DeleteCurrentUserMutation,
      DeleteCurrentUserMutationVariables
    >(DeleteCurrentUserDocument, {});
    if (error) throw error;
    return !!data?.deleteCurrentUser;
  }

  // Admin functions

  async queryUsers(input: QueryQueryUsersArgs) {
    const { data, error } = await urqlClient.executeQueryNoCache<QueryUsersQuery, QueryUsersQueryVariables>(
      QueryUsersDocument,
      input,
    );
    if (error) throw error;
    return data!.queryUsers as UserPage;
  }

  async getUser(input: QueryGetUserArgs) {
    const { data, error } = await urqlClient.executeQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, input);
    if (error) throw error;
    return data!.getUser as User;
  }

  async createUser(input: MutationCreateUserArgs) {
    const { data, error } = await urqlClient.executeMutation<CreateUserMutation, CreateUserMutationVariables>(
      CreateUserDocument,
      input,
    );
    if (error) throw error;
    return data!.createUser as User;
  }

  async updateUser(input: MutationUpdateUserArgs) {
    const { data, error } = await urqlClient.executeMutation<UpdateUserMutation, UpdateUserMutationVariables>(
      UpdateUserDocument,
      input,
    );
    if (error) throw error;
    return data!.updateUser as User;
  }

  async deleteUser(input: MutationDeleteUserArgs) {
    const { data, error } = await urqlClient.executeMutation<DeleteUserMutation, DeleteUserMutationVariables>(
      DeleteUserDocument,
      input,
    );
    if (error) throw error;
    return !!data?.deleteUser;
  }
}

export const userService = new UserService();
