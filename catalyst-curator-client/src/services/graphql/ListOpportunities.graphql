query filterOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(searchSortInput: $searchSortInput, pagingInput: $pagingInput, scope: $scope) {
    results {
      id
      priority
      status
      function
      context
      statement
      title
      solutionPathway
      statusNotes
      lastCurated
      createdAt
      campaign
      armyModernizationPriority
      echelonApplicability
      materielSolutionType
      solutionConfiguration
      DOTMLPFPPChange
      capabilitySponsor
      armyCapabilityManager
      opportunityOwnerStatuses {
        status
        statusSetPreviousAt
        statusSetRemovedAt
        isRemoved
        owner {
          organizationRole
          user {
            emailAddress
            firstName
            lastName
            org1
            org2
            org3
            org4
            phone
            altContact
          }
        }
      }
      operationalRoles
      transitionInContactLineOfEffort
      isTiCLOE
      capabilityArea
      org1
      org2
      org3
      org4
      relatedOpportunityCount
      parentOpportunityCount
      childOpportunityCount
      linkedOpportunityCount
      visibility
      attachments {
        id
      }
      links {
        id
      }
      user {
        id
        firstName
        lastName
        org1
        org2
        org3
        org4
      }
      categories {
        id
        name
      }
      stakeholders {
        id
        firstName
        lastName
        title
        emailAddress
        altEmailAddress
        phone
        org
        organizationRole
      }
      curationInfo {
        users {
          id
          firstName
          lastName
        }
        lastCurated
      }
      tenant {
        id
        name
        label
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}

query findOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(searchSortInput: $searchSortInput, pagingInput: $pagingInput, scope: $scope) {
    results {
      id
      status
      title
      ownedOpportunities {
        id
        type
        target {
          id
          title
        }
      }
      owningOpportunities {
        id
        type
        source {
          id
          title
        }
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}
