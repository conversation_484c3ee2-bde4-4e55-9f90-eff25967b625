import { observer } from 'mobx-react';
import { SafeAreaView, View } from 'react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import { Title } from './lib';
import { getDefaultTheme } from './lib/theme/ApplicationTheme';

export default observer(function App({ error: error }: { error?: Error }) {
  const message = error?.message || `App cannot be initialized`;
  const theme = getDefaultTheme();
  const {
    styles: { components, borders, paddings, margins },
    colors,
  } = theme;
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <PaperProvider theme={theme}>
        <View
          style={[
            components.sectionStyle,
            { backgroundColor: colors.border, justifyContent: 'flex-start', alignItems: 'stretch' },
          ]}
        >
          <Title style={[margins.XL, { alignSelf: 'center' }]}>{message}</Title>
        </View>
      </PaperProvider>
    </SafeAreaView>
  );
});
