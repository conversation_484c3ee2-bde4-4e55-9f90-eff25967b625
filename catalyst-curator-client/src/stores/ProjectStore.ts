import { action, computed, makeObservable, observable } from 'mobx';
import { Dates, notAfterDateValidator, notBeforeDateValidator, phoneValidator, Validator } from '../lib';
import { Store, StoreValidator } from '../lib/stores/Store';
import { attachmentService } from '../services/AttachmentService';
import {
  CreateProjectFromOpportunityInput,
  Location,
  Project,
  ProjectStakeholder,
  ProjectStakeholderType,
  ProjectStatus,
  UpdateOperator,
  UpdateProjectInput,
  UpdateProjectLinks,
} from '../services/codegen/types';
import { projectService } from '../services/ProjectService';
import { linkService } from '../services/LinkService';

/* Special Validators */
const projectStartDateValidator: StoreValidator<ProjectStore> = (startDate: Date, projectStore?: ProjectStore) => {
  const endDate = projectStore?.getValue('endDate');
  return notAfterDateValidator(startDate, Dates.asDate(endDate));
};

const projectEndDateValidator: StoreValidator<ProjectStore> = (endDate: Date, projectStore?: ProjectStore) => {
  const startDate = projectStore?.getValue('startDate');
  return notBeforeDateValidator(endDate, Dates.asDate(startDate));
};
const validators: Record<string, StoreValidator<ProjectStore>[]> = {
  startDate: [projectStartDateValidator],
  endDate: [projectEndDateValidator],
};

export class ProjectStore extends Store {
  private static readonly stores: Record<string, ProjectStore> = {};

  private _project?: Project = undefined;
  private _buffer: Partial<Project> = {};
  private _fileUploadInProgress = false;

  static async createProjectFromOpportunity(input: CreateProjectFromOpportunityInput): Promise<ProjectStore> {
    const projectResult = await projectService.createProjectFromOpportunity({ input });
    const projectStore = ProjectStore.getStoreForId(projectResult.id);
    projectStore.setProject(projectResult as Project);
    return projectStore;
  }

  static getStoreForId(id: string): ProjectStore {
    let projectStore = this.stores[id];
    if (!projectStore) {
      projectStore = new ProjectStore(id);
      this.stores[id] = projectStore;
    }
    return projectStore;
  }

  static removeStoreForId(id: string): void {
    delete this.stores.id;
  }

  private constructor(private readonly id: string) {
    super(validators);
    makeObservable<ProjectStore, '_project' | '_buffer' | '_fileUploadInProgress' | 'setProject'>(this, {
      _project: observable,
      _buffer: observable,
      _fileUploadInProgress: observable,
      project: computed,
      pendingLinks: computed,
      refresh: action,
      saveProject: action,
      setProject: action,
      stakeholderPriorityUp: action,
      stakeholderPriorityDown: action,
    });
  }

  refresh = async (): Promise<void> => {
    return this.call(async () => {
      const project = (await projectService.getProject({ id: this.id })) as Project;
      this.setProject(project);
    });
  };

  async saveProject(): Promise<void> {
    if (!this.hasErrors) {
      await this.call<void>(async () => {
        const projectResult = await projectService.updateProject({ input: this._buffer, id: this.id });
        this.mergeProject(projectResult as Project);
      });
    }
  }

  get project(): Project | undefined {
    if (this._project) return { ...this._project, ...this._buffer };
  }

  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  setValues(userValues: UpdateProjectInput) {
    this.updateValues(this._buffer, userValues);
  }

  set pendingLinks(links: UpdateProjectLinks) {
    this.pendingLinks = links;
  }

  get pendingLinks() {
    return this.pendingLinks;
  }

  getValue(name: string, defaultValue?: string) {
    // Memoized for performance
    return computed(() => {
      const value = (this.project as any)?.[name];
      return value || defaultValue;
    }).get();
  }

  getOpportunityIds(): string[] | undefined {
    return this.project?.opportunities.map((opportunity) => opportunity.id);
  }

  async addAttachment({
    file,
    uri,
    notes,
    displayName,
  }: {
    file: File;
    uri: string;
    notes?: string;
    displayName?: string;
  }): Promise<void> {
    await this.call(async () => {
      this._fileUploadInProgress = true;
      try {
        const blob = await fetch(uri).then((response) => response.blob());
        const blobFile = new File([blob], file.name, { lastModified: file.lastModified, type: file.type });

        const uploadedAttachment = await attachmentService.uploadAttachment({
          input: blobFile,
          links: { projectId: this.id },
        });

        if (displayName || notes) {
          await attachmentService.updateAttachment({
            input: {
              id: uploadedAttachment.id,
              displayName: displayName || undefined,
              notes: notes || undefined,
            },
          });
        }
      } finally {
        this._fileUploadInProgress = false;
      }
    });
    return this.refresh();
  }

  async addLink(url: string, name: string, notes?: string): Promise<void> {
    await this.call(async () => {
      await linkService.addLink({ input: { name, url, notes }, links: { projectId: this.id } });
    });
    return this.refresh();
  }

  async updateLink(linkId: string, url: string, name: string, notes?: string): Promise<void> {
    await this.call(async () => {
      await linkService.updateLink({ input: { id: linkId, name, url, notes } });
    });
    return this.refresh();
  }

  async deleteLink(linkId: string): Promise<void> {
    await this.call(async () => {
      await linkService.deleteLink({ id: linkId });
    });
    return this.refresh();
  }

  async updateAttachment(attachmentId: string, displayName?: string, notes?: string): Promise<void> {
    await this.call(async () => {
      await attachmentService.updateAttachment({ input: { id: attachmentId, displayName, notes } });
    });
    return this.refresh();
  }

  async deleteAttachment(attachmentId: string): Promise<void> {
    return this.call(async () => {
      const deleted = await attachmentService.deleteAttachment({ id: attachmentId });
      deleted && this.refresh();
    });
  }

  async getAttachment(attachmentId: string): Promise<Location> {
    return this.call(async () => {
      return attachmentService.getAttachment({ id: attachmentId });
    });
  }

  async updateProjectLinks(updateLinks?: UpdateProjectLinks): Promise<void> {
    await this.call(async () => {
      await projectService.updateProject({ input: {} as Project, id: this.id, links: updateLinks });
    });
    return this.refresh();
  }

  get fileUploadInProgress() {
    return this._fileUploadInProgress;
  }

  validateAll(): boolean {
    return super.validateAll(this._buffer);
  }

  // this orders stakeholders only relative others of the same type
  async stakeholderPriorityUp(stakeholderId: string, type: ProjectStakeholderType) {
    const projectStakeholders = this.project?.projectStakeholders || [];
    const itemIndex = projectStakeholders?.findIndex(
      (projectStakeholder) => projectStakeholder.stakeholder.id === stakeholderId && projectStakeholder.type === type,
    );
    if (!itemIndex) return;
    let moveBeforeIndex = -1;
    for (let i = itemIndex - 1; i >= 0; i--) {
      if (projectStakeholders[i].type === type) {
        moveBeforeIndex = i;
        break;
      }
    }
    //switch 'em
    if (moveBeforeIndex > -1) {
      [projectStakeholders[moveBeforeIndex], projectStakeholders[itemIndex]] = [
        projectStakeholders[itemIndex],
        projectStakeholders[moveBeforeIndex],
      ];
      return this.updateStakeholderSet(projectStakeholders);
    }
  }

  async softDeleteProject() {
    this.setValues({ status: ProjectStatus.Deleted });
    return this.saveProject();
  }

  // this orders stakeholders only relative others of the same type
  stakeholderPriorityDown(stakeholderId: string, type: ProjectStakeholderType) {
    const projectStakeholders = this.project?.projectStakeholders || [];
    const itemIndex = projectStakeholders?.findIndex(
      (projectStakeholder) => projectStakeholder.stakeholder.id === stakeholderId && projectStakeholder.type === type,
    );
    if (itemIndex < 0 || itemIndex === projectStakeholders.length - 1) return;
    let moveAfterIndex = -1;
    for (let i = itemIndex + 1; i < projectStakeholders.length; i++) {
      if (projectStakeholders[i].type === type) {
        moveAfterIndex = i;
        break;
      }
    }
    //switch 'em
    if (moveAfterIndex > -1) {
      [projectStakeholders[itemIndex], projectStakeholders[moveAfterIndex]] = [
        projectStakeholders[moveAfterIndex],
        projectStakeholders[itemIndex],
      ];
      return this.updateStakeholderSet(projectStakeholders);
    }
  }

  protected async localClearAll(): Promise<void> {}
  protected async localInitialize(): Promise<void> {}

  private updateStakeholderSet(projectStakeholders: ProjectStakeholder[]): Promise<void> {
    const links: UpdateProjectLinks = {
      projectStakeholders: [
        {
          operator: UpdateOperator.Set,
          items: projectStakeholders.map((projectStakeholder) => ({
            id: projectStakeholder.stakeholder.id,
            type: projectStakeholder.type,
          })),
        },
      ],
    };
    return this.updateProjectLinks(links);
  }

  private mergeProject(project: Partial<Project>) {
    this._project && this.setProject({ ...this._project, ...this._buffer, ...project });
  }

  private clearBuffer() {
    this._buffer = {};
    this.clearErrors();
  }

  private setProject(project: Project) {
    this._project = project;
    this.clearBuffer();
  }
}
