import { MenuGroup, MenuItem } from '../../lib';
import { Priority } from '../../lib/Priority';
import { PRIORITY_LABELS } from '../../lib/ui/organisms/PriorityMenu';
import { ACM_OPTIONS_BY_CDID } from '../../pages/sections/opportunity/ACMDropdown';
import { CapabilityAreaItems } from '../../pages/sections/opportunity/CapabilityAreaDropdown';
import {
  DOTMLPFPP_ITEMS,
  MATERIEL_SOLUTION_ITEMS,
} from '../../pages/sections/opportunity/curated-opportunity-groups/Analysis';
import { ArmyModernizationPrioritiesItems } from '../../pages/sections/opportunity/curated-opportunity-groups/ArmyModernizationPriorities';
import { EchelonApplicabilityItems } from '../../pages/sections/opportunity/curated-opportunity-groups/EchelonApplicability';
import { CDID_ITEMS } from '../../pages/sections/opportunity/curated-opportunity-groups/Requirement';
import { OperationalRolesItems } from '../../pages/sections/opportunity/OperationalRolesDropdown';
import { SearchOperator } from '../../services/codegen/types';
import { FilterInfoStore } from '../FilterInfoStore';

export function priorityFilterStore(): FilterInfoStore {
  const priorityFilterInfo = new FilterInfoStore();
  priorityFilterInfo.fieldOrGroup = 'FIELD';
  priorityFilterInfo.groupName = 'priority';
  priorityFilterInfo.info = [
    { label: PRIORITY_LABELS[Priority.HIGH], value: Priority.HIGH },
    { label: PRIORITY_LABELS[Priority.MEDIUM], value: Priority.MEDIUM },
    { label: PRIORITY_LABELS[Priority.LOW], value: Priority.LOW },
    { label: PRIORITY_LABELS[Priority.NONE], value: Priority.NONE },
  ];
  return priorityFilterInfo;
}

export function relationshipsFilterStore(): FilterInfoStore {
  const relationshipsFilterInfo = new FilterInfoStore();
  relationshipsFilterInfo.fieldOrGroup = 'GROUP';
  relationshipsFilterInfo.groupName = 'relationships';
  relationshipsFilterInfo.info = [
    {
      label: 'Parents',
      searchField: { fieldNames: ['childOpportunityCount'], searchValue: 1, operator: SearchOperator.Gte },
    },
    {
      label: 'Linked',
      searchField: { fieldNames: ['linkedOpportunityCount'], searchValue: 1, operator: SearchOperator.Gte },
    },
    {
      label: 'Children',
      searchField: { fieldNames: ['parentOpportunityCount'], searchValue: 1, operator: SearchOperator.Gte },
    },
    {
      label: 'None',
      searchField: { fieldNames: ['relatedOpportunityCount'], searchValue: 1, operator: SearchOperator.Lt },
    },
  ];
  return relationshipsFilterInfo;
}

export function statusFilterStore(): FilterInfoStore {
  const statusFilterInfo = new FilterInfoStore();
  statusFilterInfo.fieldOrGroup = 'FIELD';
  statusFilterInfo.groupName = 'status';
  statusFilterInfo.info = [
    { label: 'In Curation', value: 'In Curation' },
    { label: 'On Hold', value: 'On Hold' },
    { label: 'Resolved', value: 'Resolved' },
    { label: 'Not Selected', value: 'Not Selected' },
    { label: 'Duplicate', value: 'Duplicate' },
    { label: 'Advanced', value: 'Advanced' },
    { label: 'Archived', value: 'Archived' },
    { label: 'Approved', value: 'Approved' },
  ];
  return statusFilterInfo;
}

export function capabilityAreaFilterStore(): FilterInfoStore {
  const capabilityAreaFilterInfo = new FilterInfoStore();
  capabilityAreaFilterInfo.fieldOrGroup = 'FIELD';
  capabilityAreaFilterInfo.groupName = 'capabilityArea';

  capabilityAreaFilterInfo.info = CapabilityAreaItems.flatMap((group) => {
    const entries = [{ label: group.label, value: group.value }];
    return entries;
  });

  capabilityAreaFilterInfo.useSearchBar = true;
  return capabilityAreaFilterInfo;
}

export function operationalRolesFilterStore(): FilterInfoStore {
  const operationalRolesFilterInfo = new FilterInfoStore();
  operationalRolesFilterInfo.fieldOrGroup = 'FIELD';
  operationalRolesFilterInfo.groupName = 'operationalRoles';

  operationalRolesFilterInfo.info = OperationalRolesItems.flatMap((group) => {
    const entries = [{ label: group.item.label, value: group.item.value }];

    if (group.children?.length) {
      const childEntries = group.children
        .filter((child) => !child.isParentSelector)
        .map((child) => ({
          label: child.item.label,
          value: child.item.value,
        }));
      entries.push(...childEntries);
    }

    return entries;
  });
  operationalRolesFilterInfo.useSearchBar = true;
  return operationalRolesFilterInfo;
}

export function echelonApplicabilityFilterStore(): FilterInfoStore {
  const echelonApplicabilityFilterInfo = new FilterInfoStore();
  echelonApplicabilityFilterInfo.fieldOrGroup = 'FIELD';
  echelonApplicabilityFilterInfo.groupName = 'echelonApplicability';
  echelonApplicabilityFilterInfo.info = EchelonApplicabilityItems.map((item) => ({
    label: item.label,
    value: item.value,
  }));
  return echelonApplicabilityFilterInfo;
}

export function armyModernizationPriorityFilterStore(): FilterInfoStore {
  const armyModernizationPriorityFilterInfo = new FilterInfoStore();
  armyModernizationPriorityFilterInfo.fieldOrGroup = 'FIELD';
  armyModernizationPriorityFilterInfo.groupName = 'armyModernizationPriority';
  armyModernizationPriorityFilterInfo.info = ArmyModernizationPrioritiesItems.map((item) => ({
    label: item.label,
    value: item.value,
  }));
  return armyModernizationPriorityFilterInfo;
}

export function isTiCLOEFilterStore(): FilterInfoStore {
  const isTiCLOEFilterInfo = new FilterInfoStore();
  isTiCLOEFilterInfo.fieldOrGroup = 'FIELD';
  isTiCLOEFilterInfo.groupName = 'isTiCLOE';

  isTiCLOEFilterInfo.info = [
    {
      label: 'Yes',
      value: 'Yes',
    },
    {
      label: 'No',
      value: 'No',
    },
    {
      label: 'Undefined',
      value: 'Undefined',
    },
  ];
  return isTiCLOEFilterInfo;
}

export function materielSolutionTypeFilterStore(): FilterInfoStore {
  const materielSolutionTypeFilterInfo = new FilterInfoStore();
  materielSolutionTypeFilterInfo.fieldOrGroup = 'FIELD';
  materielSolutionTypeFilterInfo.groupName = 'materielSolutionType';
  materielSolutionTypeFilterInfo.info = MATERIEL_SOLUTION_ITEMS.map((item) => ({
    label: item.label,
    value: item.value,
  }));
  return materielSolutionTypeFilterInfo;
}

export function DOTMLPFPPChangeFilterStore(): FilterInfoStore {
  const DOTMLPFPPChangeFilterInfo = new FilterInfoStore();
  DOTMLPFPPChangeFilterInfo.fieldOrGroup = 'FIELD';
  DOTMLPFPPChangeFilterInfo.groupName = 'DOTMLPFPPChange';
  DOTMLPFPPChangeFilterInfo.info = DOTMLPFPP_ITEMS.map((item) => ({
    label: item.label,
    value: item.value,
  }));
  return DOTMLPFPPChangeFilterInfo;
}

export function warFightingFunctionFilterStore(): FilterInfoStore {
  const warFightingFunctionFilterInfo = new FilterInfoStore();
  warFightingFunctionFilterInfo.fieldOrGroup = 'GROUP';
  warFightingFunctionFilterInfo.groupName = 'function';
  warFightingFunctionFilterInfo.info = [
    { label: 'APPROVED', value: 'Approved' },
    { label: 'PENDING', value: 'Pending' },
    { label: 'ARCHIVED', value: 'Archived' },
  ];
  warFightingFunctionFilterInfo.useSearchBar = true;
  return warFightingFunctionFilterInfo;
}

export function capabilitySponsorFilterStore(): FilterInfoStore {
  const capabilitySponsorFilterInfo = new FilterInfoStore();
  capabilitySponsorFilterInfo.fieldOrGroup = 'FIELD';
  capabilitySponsorFilterInfo.groupName = 'capabilitySponsor';
  capabilitySponsorFilterInfo.info = CDID_ITEMS.map((item) => ({
    label: item.label,
    value: item.value,
  }));
  capabilitySponsorFilterInfo.useSearchBar = true;
  return capabilitySponsorFilterInfo;
}

export function armyCapabilityManagerFilterStore(): FilterInfoStore {
  function flattenACMOptions(acmOptionsByCDID: Record<string, MenuGroup[]>): MenuItem[] {
    const entries: MenuItem[] = [];

    const walk = (groups: MenuGroup[]) => {
      for (const group of groups) {
        if (group.isParentSelector) continue;
        entries.push({ label: group.item.label, value: group.item.value });
        if (group.children) {
          walk(group.children);
        }
      }
    };

    for (const groupList of Object.values(acmOptionsByCDID)) {
      walk(groupList);
    }

    return entries;
  }

  const armyCapabilityManagerFilterInfo = new FilterInfoStore();
  armyCapabilityManagerFilterInfo.fieldOrGroup = 'FIELD';
  armyCapabilityManagerFilterInfo.groupName = 'armyCapabilityManager';

  armyCapabilityManagerFilterInfo.info = flattenACMOptions(ACM_OPTIONS_BY_CDID);
  armyCapabilityManagerFilterInfo.useSearchBar = true;
  return armyCapabilityManagerFilterInfo;
}
