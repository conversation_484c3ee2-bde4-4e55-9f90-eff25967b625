import {
  Poppins_200ExtraLight,
  Poppins_300Light,
  Poppins_400Regular,
  Poppins_600SemiBold,
  Poppins_700Bold,
} from '@expo-google-fonts/poppins';

import {
  SourceSans3_200ExtraLight,
  SourceSans3_300Light,
  SourceSans3_400Regular,
  SourceSans3_600SemiBold,
  SourceSans3_700Bold,
} from '@expo-google-fonts/source-sans-3';

import * as Font from 'expo-font';
import { action, computed, makeObservable, observable } from 'mobx';
import { Image } from 'react-native';
import defaultTenantConfig from '../assets/defaultTenantConfig.json';
import { Store } from '../lib/stores/Store';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { CuratorStores } from '../platform/initializers';
import { ApplicationMeta, TenantInfo } from '../services/codegen/types';
import { tenantService } from '../services/TenantService';
import { getDefaultCurationTheme, loadCurationThemeByTenant } from '../lib/theme/ApplicationTheme';
import { AssertedAppMeta, TenantConfig, TenantContent, TenantTheme } from '../lib/types';
import { OrganizationStore } from './OrganizationStore';
import { TenantConfigProxy } from '../lib/TenantConfigProxy';
import { AppStyles } from '../lib';
import { getStyleOverrides } from '../style/LocalStyles';
import { Objects } from '../lib/Objects';

export class TenantStore extends Store {
  tenantConfig: TenantConfig = TenantConfigProxy.getProxy(defaultTenantConfig, 'curation');
  tenantContent: TenantContent | null = null;
  filterOtherPrivateOpportunities: boolean | null = null;
  theme: ReactNativePaper.ThemeProp = {} as any;
  tenantLoaded = false;
  serverVersion = '';

  private _tenantHandleOrAlias: string;

  constructor(tenantHandleOrAlias: string) {
    super();
    this._tenantHandleOrAlias = tenantHandleOrAlias;
    makeObservable<TenantStore, '_tenantHandleOrAlias'>(this, {
      _tenantHandleOrAlias: observable,
      tenantLoaded: observable,
      theme: observable.shallow,
      loadTenant: action,
      tenantHandleOrAlias: computed,
    });
  }

  async loadTenant(tenantHandle?: string): Promise<void> {
    const tenantInfo = await this.getTenantInfo(tenantHandle || this._tenantHandleOrAlias);
    if (!tenantInfo) throw Error(`Tenant '${this._tenantHandleOrAlias}' not found on this server`);
    if (tenantHandle) this._tenantHandleOrAlias = tenantHandle;
    this.tenantLoaded = false;
    this.tenantConfig = TenantConfigProxy.getProxy(tenantInfo.meta?.config, 'curation');
    this.tenantContent = tenantInfo.meta?.content;

    if (tenantInfo.meta?.filterOtherPrivateOpportunities === undefined) {
      this.filterOtherPrivateOpportunities = true;
    } else this.filterOtherPrivateOpportunities = tenantInfo.meta?.filterOtherPrivateOpportunities;

    StoresProvider.get<CuratorStores>().setStore(
      'OrganizationStore',
      new OrganizationStore(this.tenantConfig.orgValues),
    );
    this.serverVersion = tenantInfo.serverVersion;
    // custom themes for curation would go here
    //await Promise.all([this.loadThemeAndStyles(tenantInfo.meta?.curationTheme), this.loadFonts()]);
    await Promise.all([this.loadThemeAndStyles(), this.loadFonts()]);
    if (this.tenantConfig.sLogo) {
      this.tenantConfig.sLogoSize = await this.getLogoSize(this.tenantConfig.sLogo);
    }
    this.tenantLoaded = true;
  }

  get tenantHandleOrAlias(): string {
    return this._tenantHandleOrAlias;
  }

  getImageSourceProp(uri: string | undefined): { uri: string } {
    return uri ? { uri } : { uri: '' };
  }

  getApplicationMeta(): Partial<ApplicationMeta> {
    const anaMeta = this.tenantConfig.curation?.curationMeta?.anaTable;
    const oppMeta = this.tenantConfig.curation?.curationMeta?.oppTable;
    const projMeta = this.tenantConfig.curation?.curationMeta?.projTable;

    const appMeta = {
      curationMeta: {
        anaTable: anaMeta
          ? { ...Objects.deepCopy({ ...this.tenantConfig.curation?.curationMeta?.anaTable }) }
          : getDefaultApplicationMeta().curationMeta?.anaTable,
        oppTable: oppMeta
          ? { ...Objects.deepCopy({ ...this.tenantConfig.curation?.curationMeta?.oppTable }) }
          : getDefaultApplicationMeta().curationMeta?.oppTable,
        projTable: projMeta
          ? { ...Objects.deepCopy({ ...this.tenantConfig.curation?.curationMeta?.projTable }) }
          : getDefaultApplicationMeta().curationMeta?.projTable,
      },
    };
    if (this.filterOtherPrivateOpportunities) {
      //remove the visibility column from anaTable and return it
      const newAnaTable = appMeta.curationMeta.anaTable;
      newAnaTable.cols = newAnaTable.cols.filter((col: any) => col.colId !== 'visibility');
      appMeta.curationMeta.anaTable = newAnaTable;
    }

    return appMeta;
  }

  async localClearAll(): Promise<void> {}
  protected async localInitialize(): Promise<void> {}

  private async getTenantInfo(handleOrAlias?: string): Promise<TenantInfo> {
    return tenantService.getTenantInfo({ handleOrAlias });
  }

  private async loadFonts(): Promise<void> {
    await Font.loadAsync({
      SourceSans3_200ExtraLight,
      SourceSans3_400Regular,
      SourceSans3_300Light,
      SourceSans3_600SemiBold,
      SourceSans3_700Bold,
      Poppins_200ExtraLight,
      Poppins_300Light,
      Poppins_400Regular,
      Poppins_600SemiBold,
      Poppins_700Bold,
    });
  }

  private async loadThemeAndStyles(tenantTheme?: TenantTheme): Promise<ReactNativePaper.ThemeProp> {
    const theme = !!tenantTheme ? await loadCurationThemeByTenant(tenantTheme) : getDefaultCurationTheme();
    // attach all additional styles defined through 'AppStyles' here
    (theme as ReactNativePaper.ThemeProp).styles = AppStyles.generate(
      theme as ReactNativePaper.ThemeProp,
      getStyleOverrides(theme),
    );
    this.theme = theme;
    return theme;
  }

  // Note in the future, we can use an observable record to cache any image dimensions that we need to
  private async getLogoSize(logo?: string): Promise<{ width: number; height: number } | undefined> {
    if (!logo) return undefined;
    return new Promise((resolve, reject) => {
      Image.getSize(
        logo,
        (width, height) => resolve({ width, height }),
        () => resolve(undefined),
      );
    });
  }
}

// Note: Don't include optional columns here as this is a default
// The tenant config will override this with option optional columns if needed
const getDefaultApplicationMeta = () => {
  const meta = {
    curationMeta: {
      oppTable: {
        cols: [
          { colId: 'status', colWidth: 115, pinned: false, hidden: false },
          {
            colId: 'priority',
            colWidth: 125,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'createdAt',
            colWidth: 110,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'lastCurated',
            colWidth: 140,
            pinned: false,
            hidden: false,
          },
          { colId: 'title', colWidth: 250, pinned: false, hidden: false },
          { colId: 'statement', colWidth: 350, pinned: false, hidden: false },
          { colId: 'context', colWidth: 350, pinned: false, hidden: false },
          {
            colId: 'capabilityArea',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          { colId: 'function', colWidth: 225, pinned: false, hidden: false },
          {
            colId: 'operationalRoles',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'echelonApplicability',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'armyModernizationPriority',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'isTiCLOE',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'categories',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'materielSolutionType',
            colWidth: 220,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'DOTMLPFPPChange',
            colWidth: 280,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'relatedOpportunityCount',
            colWidth: 100,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'capabilitySponsor',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'armyCapabilityManager',
            colWidth: 240,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'opportunityOwnerStatuses',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'org1',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'stakeholders',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'solutionPathway',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'attachments',
            colWidth: 120,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'statusNotes',
            colWidth: 350,
            pinned: false,
            hidden: false,
          },
          { colId: 'visibility', colWidth: 125, pinned: false, hidden: false },
        ],
        sortCols: [],
      },
      anaTable: {
        cols: [
          { colId: 'tenant', colWidth: 200, pinned: false, hidden: false },
          { colId: 'status', colWidth: 140, pinned: false, hidden: false },
          {
            colId: 'priority',
            colWidth: 125,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'createdAt',
            colWidth: 110,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'lastCurated',
            colWidth: 140,
            pinned: false,
            hidden: false,
          },
          { colId: 'title', colWidth: 250, pinned: false, hidden: false },
          { colId: 'statement', colWidth: 350, pinned: false, hidden: false },
          { colId: 'context', colWidth: 350, pinned: false, hidden: false },
          {
            colId: 'capabilityArea',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          { colId: 'function', colWidth: 225, pinned: false, hidden: false },
          {
            colId: 'operationalRoles',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'echelonApplicability',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'armyModernizationPriority',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'isTiCLOE',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'categories',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'materielSolutionType',
            colWidth: 220,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'DOTMLPFPPChange',
            colWidth: 280,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'relatedOpportunityCount',
            colWidth: 100,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'capabilitySponsor',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'armyCapabilityManager',
            colWidth: 240,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'opportunityOwnerStatuses',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'org1',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'stakeholders',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'solutionPathway',
            colWidth: 200,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'attachments',
            colWidth: 120,
            pinned: false,
            hidden: false,
          },
          {
            colId: 'statusNotes',
            colWidth: 350,
            pinned: false,
            hidden: false,
          },
          { colId: 'visibility', colWidth: 125, pinned: false, hidden: false },
        ],
        sortCols: [],
      },
      projTable: {
        cols: [
          { colId: 'status', colWidth: 135, pinned: false, hidden: false },
          { colId: 'title', colWidth: 250, pinned: false, hidden: false },
          { colId: 'type', colWidth: 250, pinned: false, hidden: false },
          { colId: 'startDate', colWidth: 185, pinned: false, hidden: false },
          { colId: 'div_stakeholders', colWidth: 250, pinned: false, hidden: false },
          { colId: 'perf_stakeholders', colWidth: 250, pinned: false, hidden: false },
          {
            colId: 'statusNotes',
            colWidth: 350,
            pinned: false,
            hidden: false,
          },
        ],
        sortCols: [],
      },
      status: {
        reasons: [
          { name: 'Existing Solution', statusName: 'resolved' },
          { name: 'Solution Developed', statusName: 'resolved' },
          { name: 'Technology Not Viable', statusName: 'not selected' },
          { name: 'Command Priorities', statusName: 'not selected' },
          { name: 'Resource Constraints: Funding', statusName: 'not selected' },
          { name: 'Resource Constraints: Personnel', statusName: 'not selected' },
          { name: 'Resource Constraints: Materiel', statusName: 'not selected' },
          { name: 'Regulatory Constraint', statusName: 'not selected' },
        ],
      },
    },
  } as Partial<AssertedAppMeta>;

  return meta;
};
