import { View, useWindowDimensions } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Dates, Title } from '../lib';
import { Router } from '../platform/Router';
import { MainStackParamList } from '../routing/screens';
import { Project, ProjectStakeholder, ProjectStakeholderType } from '../services/codegen/types';
import UserStore from '../stores/UserStore';
import ProjectsStore from '../stores/ProjectsStore';
import { FilterProjectsBar } from './sections/project/FilterProjectsBar';
import { GenericGrid } from '../appComponents/GenericGrid';
import { GridControl } from '../appComponents/web/Grid.web';
import { ProjectStore } from '../stores/ProjectStore';
import { ColDef } from 'ag-grid-community';
import { mapStatusToColor } from '../lib/OpportunityStatus';
import { Text } from '../lib/ui/atoms/Text';
import { SMALL_DEVICE_BREAKPOINT } from '../constants/breakpoints';
import { HiddenView } from '../lib/ui/atoms/HiddenView';
import { ClickableText } from '../lib/ui/atoms/ClickableText';
import { ApplicationMetaStore } from '../stores/ApplicationMetaStore';
import { useLocalObservable } from 'mobx-react';

interface ProjectsProps {
  projectsStore: ProjectsStore;
  router: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
}

interface Locals {
  gridControl?: GridControl;
  selectedProjectStore?: ProjectStore;
  setGridControl: (gridControl: GridControl) => void;
  stSelectedProjectStore: (projectStore: ProjectStore) => void;
}
export const ProjectsPage = withTheme(({ projectsStore, router, theme, userStore }: ProjectsProps) => {
  const {
    colors,
    styles: { components, margins },
    fonts,
    fontSizes,
  } = theme;
  const { userMetaStore } = userStore;
  const localStore = useLocalObservable<Locals>(() => ({
    gridControl: undefined,
    selectedProjectStore: undefined,
    setGridControl(gridControl: GridControl) {
      this.gridControl = gridControl;
    },
    stSelectedProjectStore(projectStore: ProjectStore) {
      this.selectedProjectStore = projectStore;
    },
  }));

  const colDefsMap = getColDefsMap(theme);

  const { width } = useWindowDimensions();
  const shouldFilterStack = width <= SMALL_DEVICE_BREAKPOINT;

  return (
    <View style={[components.flexAll, margins.BottomL]}>
      <View style={[components.flexAll, components.panelStyle, components.shadow]}>
        <View
          style={[
            {
              flexDirection: shouldFilterStack ? 'column' : 'row',
              alignItems: 'center',
              gap: shouldFilterStack ? 8 : 0,
            },
          ]}
        >
          <Title style={[fonts.mediumTitle, fontSizes.medium, { color: colors.primary }]}>
            Project Primer Dashboard
          </Title>
          <FilterProjectsBar projectsStore={projectsStore} style={[margins.BottomXS]} />
        </View>
        <View style={[{ flexDirection: 'row', alignSelf: 'flex-end' }]}>
          <HiddenView getVisible={() => !!userMetaStore.projTable?.hasHidden}>
            <ClickableText
              style={[components.smallClickableTextStyle, margins.RightML]}
              onPress={() => handleRestoreHidden(userStore, userMetaStore, localStore)}
            >
              SHOW HIDDEN
            </ClickableText>
          </HiddenView>
          <ClickableText style={[components.smallClickableTextStyle]} onPress={() => handleResetTable(userStore)}>
            RESET TABLE
          </ClickableText>
        </View>
        <GenericGrid<Project>
          theme={theme}
          store={projectsStore}
          router={router}
          userStore={userStore}
          colDefsMap={colDefsMap}
          sortableCols={sortableCols}
          onRowPress={(router, event, projectsStore) => {
            projectsStore.selection = event.data.id;
            handlePressProject(event.data.id, router);
          }}
          getGridControl={() => localStore.gridControl}
          onGridReady={(gridControl) => {
            localStore.setGridControl(gridControl);
          }}
          table={userMetaStore.projTable}
        />
      </View>
    </View>
  );
});

const handleRestoreHidden = (userStore: UserStore, userMetaStore: ApplicationMetaStore, localStore: Locals) => {
  userMetaStore.projTable?.showAllColumns();
  localStore.gridControl?.showAllColumns();
  userStore.saveUserApplicationMeta();
};

const handleResetTable = (userStore: UserStore) => {
  userStore.resetProjTableMeta();
};

const handlePressProject = (id: string, router?: Router<MainStackParamList>) => {
  router?.navigate('project', { id });
};
const sortableCols = ['status', 'title', 'type', 'startDate', 'statusNotes'];

const getColDefsMap = (
  theme: ReactNativePaper.ThemeProp,
): {
  [key: string]: ColDef;
} => {
  const {
    styles: { fonts, components, margins, paddings },
  } = theme;
  return {
    status: {
      field: 'status',
      headerName: 'Status',
      cellRenderer: (params: any) => {
        const status = params.data?.status;
        return <Text style={[{ color: mapStatusToColor(theme, status) }, fonts.bold]}>{status}</Text>;
      },
    },
    title: { field: 'title', headerName: 'Primer Title' },
    type: { field: 'type', headerName: 'Project Type' },
    startDate: {
      field: 'startDate',
      headerName: 'Start Date / End Date',
      cellRenderer: (params: any) => {
        if (!params.data?.startDate || !params.data?.endDate) return '';
        return `${Dates.asSimpleDateString(params.data?.startDate)} -- ${Dates.asSimpleDateString(params.data?.endDate)}`;
      },
    },
    div_stakeholders: {
      field: 'div_stakeholders',
      headerName: 'Division Stakeholders +',
      autoHeight: true,
      wrapText: true,
      cellRenderer: (params: any) => {
        const divisionStakeholders = params.data?.projectStakeholders.filter(
          (stakeholder: ProjectStakeholder) => stakeholder.type === ProjectStakeholderType.Division,
        );
        const items = divisionStakeholders
          .slice(0, 2)
          .map((stakeholder: ProjectStakeholder) => stakeholder.stakeholder.name || '');

        if (divisionStakeholders.length > 2) items.push('...');
        return items.join(', ');
      },
    },
    perf_stakeholders: {
      field: 'perf_stakeholders',
      headerName: 'Performer Stakeholders +',
      cellRenderer: (params: any) => {
        const divisionStakeholders = params.data?.projectStakeholders.filter(
          (stakeholder: ProjectStakeholder) => stakeholder.type === ProjectStakeholderType.Performer,
        );
        const items = divisionStakeholders
          .slice(0, 2)
          .map((stakeholder: ProjectStakeholder) => stakeholder.stakeholder.name || '');

        if (divisionStakeholders.length > 2) items.push('...');

        return items.join(', ');
      },
    },
    statusNotes: { field: 'statusNotes', headerName: 'Status Notes' },
  } as Record<string, any>;
};
