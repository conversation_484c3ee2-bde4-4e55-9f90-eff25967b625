import { ScrollView, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, Dates, LabeledDropdownMenu, LabeledInput, LabeledTextInput, TextInputProps } from '../lib';
import { Group } from '../lib/ui/atoms/Group';
import { HiddenView } from '../lib/ui/atoms/HiddenView';
import { AttachmentInfo, FileAttachments } from '../lib/ui/molecules/FileAttachments';
import { LabeledValue } from '../lib/ui/molecules/LabeledValue';
import { LinkedItem } from '../lib/ui/molecules/LinkedItem';
import { Router } from '../platform/Router';
import { MainStackParamList } from '../routing/screens';
import { ProjectStakeholderType, ProjectStatus, UpdateOperator, UpdateProjectLinks } from '../services/codegen/types';
import { CategoryStore, StakeholderStore } from '../stores';
import { ProjectStore } from '../stores/ProjectStore';
import UserStore from '../stores/UserStore';
import { CategorySelector } from './sections/CategorySelector';
import { ProjectDateInput } from './sections/project/ProjectDateInput';
import { ProjectHeader } from './sections/project/ProjectHeader';
import { AdminDeleteProject } from './sections/project/DeleteProject';
import { ProjectStakeholders } from './sections/project/ProjectStakeholders';
import { ProjectAttachments } from './sections/project/ProjectAttachments';

const _styles: Record<string, StyleProp<ViewStyle>> = StyleSheet.create({
  content: {
    flexGrow: 1,
    marginBottom: 25,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
  },
});

interface ProjectProps {
  projectStore: ProjectStore;
  router: Router<MainStackParamList>;
  userStore: UserStore;
  categoryStore: CategoryStore;
  stakeholderStore: StakeholderStore;
  theme: ReactNativePaper.ThemeProp;
}

export const ProjectPage = withTheme(
  ({ projectStore, categoryStore, stakeholderStore, router, theme, userStore }: ProjectProps) => {
    const {
      colors,
      fonts,
      styles: { margins, paddings, borders, components },
    } = theme;
    return (
      <ScrollView>
        <View style={[components.flexAll, margins.BottomL]}>
          <ProjectHeader projectStore={projectStore} onDownload={() => {}} router={router} />
          <View style={[components.pageContainerConstraints]}>
            <View style={[components.flexAll, components.globalPageConstraints]}>
              <View style={[_styles.content]}>
                <View style={[{ backgroundColor: colors.background }, paddings.TopL]}>
                  <View style={[margins.BottomML, { flex: 1 }]}>
                    <Group title="Summary">
                      {getLabelTextInput({
                        projectStore,
                        fieldName: 'title',
                        labelText: 'Project Title',
                        textInputProps: { multiline: false, maxLength: 255 },
                        style: [components.rowStyle],
                      })}
                      {getLabelTextInput({
                        projectStore,
                        fieldName: 'summary',
                        labelText: 'Project Summary',
                        textInputProps: {
                          multiline: true,
                          numberOfLines: 2,
                          placeholder: 'Summarize the project here',
                        },
                        style: [components.rowStyle],
                      })}
                      <View
                        style={[
                          components.rowStyle,
                          {
                            flexDirection: 'row',
                            justifyContent: 'space-evenly',
                            alignItems: 'center',
                            flexWrap: 'wrap',
                            width: '100%',
                          },
                        ]}
                      >
                        <LabeledInput
                          labelText={'Source Innovation Opportunity'}
                          style={[{ flexGrow: 1, width: '100%' }]}
                        >
                          <LinkedItem
                            getLinkText={() => {
                              return projectStore.project?.opportunities[0]?.title || '';
                            }}
                            style={{ maxWidth: 680, width: '100%' }}
                            onPress={() => projectStore && handlePressProject(projectStore, router)}
                          />
                        </LabeledInput>
                        <LabeledValue
                          style={[margins.RightS, { flexDirection: 'column', flexGrow: 1 }]}
                          getValue={() => Dates.asSimpleDateString(projectStore.project?.createdAt)}
                          labelText={'Creation Date'}
                          labelProps={{ textStyle: [margins.BottomM] }}
                        />
                        <LabeledValue
                          style={[margins.RightS, { flexDirection: 'column', flexGrow: 1 }]}
                          getValue={() => projectStore.project?.creator?.emailAddress}
                          labelText={'Created By'}
                          labelProps={{ textStyle: [margins.BottomM] }}
                        />
                      </View>
                    </Group>
                    <Group title="Objectives">
                      {getLabelTextInput({
                        projectStore,
                        fieldName: 'background',
                        labelText: 'Project Background',
                        textInputProps: { numberOfLines: 8, placeholder: 'Background of project goes here' },
                        style: components.rowStyle,
                      })}
                      <View style={[components.rowStyle, { flexDirection: 'row' }]}>
                        <ProjectDateInput
                          style={[margins.RightM]}
                          fieldName="startDate"
                          labelText="Project Start Date"
                          projectStore={projectStore}
                          onValidDate={(date) => updateAndSave(projectStore, date, 'startDate')}
                        />
                        <ProjectDateInput
                          fieldName="endDate"
                          labelText="Final Deliverable Due Date"
                          projectStore={projectStore}
                          onValidDate={(date) => updateAndSave(projectStore, date, 'endDate')}
                        />
                      </View>
                      <View style={[components.rowStyle, { flexDirection: 'row' }]}>
                        <LabeledDropdownMenu
                          style={[components.elementStyle, margins.RightM, { flex: 1 }]}
                          dropdownMenuProps={{
                            getMenuItems: () => projectTypeItems,
                            onItemSelected: (item) => updateAndSave(projectStore, item.value, 'type'),
                            getValue: () => ({
                              label: projectStore.getValue('type'),
                              value: projectStore.getValue('type'),
                            }),
                          }}
                          labelText={'Project Type'}
                        />
                        <HiddenView getVisible={() => projectStore.getValue('type') === 'Other'} style={{ flex: 1 }}>
                          {getLabelTextInput({
                            projectStore,
                            fieldName: 'otherType',
                            labelText: 'Other Type',
                            textInputProps: { multiline: false, placeholder: 'Name of project type' },
                            style: StyleSheet.flatten([components.elementStyle, { flex: 1 }]),
                          })}
                        </HiddenView>
                      </View>
                      {getLabelTextInput({
                        projectStore,
                        fieldName: 'goals',
                        labelText: 'Goals and Objectives',
                        textInputProps: { numberOfLines: 5, placeholder: 'Goals of project go here' },
                        style: components.rowStyle,
                      })}
                    </Group>
                    <Group title="Categories" style={{ zIndex: 3 }}>
                      <CategorySelector
                        onPropertyCategoryChanged={(op, id) => handleOnCategoryChanged(projectStore, op, id)}
                        categoryStore={categoryStore}
                        getCategories={() => projectStore.project?.categories}
                        style={[components.rowStyle, { zIndex: 3 }]}
                        getEditable={() => true}
                      />
                    </Group>
                    <Group title="Stakeholders" style={{ zIndex: 2 }}>
                      <ProjectStakeholders
                        onStakeholderChanged={handleOnStakeholderChanged}
                        projectStore={projectStore}
                        stakeholderStore={stakeholderStore}
                      />
                    </Group>
                    <ProjectAttachments userStore={userStore} projectStore={projectStore} />
                    <Group title="Project Status" style={{ zIndex: 1 }} noBorder>
                      <LabeledDropdownMenu
                        style={[components.rowStyle]}
                        dropdownMenuProps={{
                          getMenuItems: () => statusItems,
                          onItemSelected: (item) => updateAndSave(projectStore, item.value, 'status'),
                          getValue: () => ({
                            label: projectStore.getValue('status'),
                            value: projectStore.getValue('status'),
                          }),
                        }}
                        labelText={'Status'}
                      />
                      {getLabelTextInput({
                        projectStore,
                        fieldName: 'statusNotes',
                        labelText: 'Status Notes',
                        textInputProps: {
                          numberOfLines: 5,
                          placeholder: 'Additional notes on the status of the project',
                        },
                        style: components.rowStyle,
                      })}
                    </Group>
                  </View>
                  <AdminDeleteProject
                    userStore={userStore}
                    projectStore={projectStore}
                    onDeleteProject={() => handleDeleteProject(projectStore, router)}
                    style={[{ alignSelf: 'center' }]}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    );
  },
);

const getLabelTextInput = (params: {
  projectStore: ProjectStore;
  fieldName: string;
  labelText: string;
  style?: StyleProp<ViewStyle>;
  textInputProps: Partial<TextInputProps>;
}) => {
  const { projectStore, fieldName, labelText, style, textInputProps } = params;
  return (
    <LabeledTextInput
      style={style}
      textInputProps={{
        multiline: true,
        numberOfLines: 1,
        spellcheck: false,

        getValue: () => projectStore.getValue(fieldName),
        setValue: (value) => projectStore.setValue(fieldName, value),
        onDebounceValue: () => projectStore.saveProject(),
        ...textInputProps,
      }}
      labelText={labelText}
    />
  );
};

const handleOnCategoryChanged = async (projectStore: ProjectStore, operator: UpdateOperator, id: string) => {
  const updateInput: UpdateProjectLinks = {};
  updateInput['categories'] = [{ operator: operator, ids: [id] }];
  await projectStore.updateProjectLinks(updateInput);
};

const handleOnStakeholderChanged = (
  projectStore: ProjectStore,
  operator: UpdateOperator,
  id: string,
  type: ProjectStakeholderType,
): Promise<void> => {
  const updateInput: UpdateProjectLinks = {};
  updateInput['projectStakeholders'] = [{ operator: operator, items: [{ id, type }] }];
  return projectStore.updateProjectLinks(updateInput);
};

const handleDeleteProject = async (projectStore: ProjectStore, router: Router<MainStackParamList>) => {
  await projectStore.softDeleteProject();
  router.goBack();
};

const handlePressProject = (projectStore?: ProjectStore, router?: Router<MainStackParamList>) => {
  const oppIds = projectStore?.getOpportunityIds();
  if (!projectStore || !oppIds?.length || !router) return;
  router.navigate('curation', { id: oppIds[0] });
};

const updateAndSave = (projectStore: ProjectStore, value: any | undefined, name: string) => {
  projectStore.setValue(name, value || null);
  projectStore.saveProject();
};

const projectTypeItems = [
  { label: 'Pathfinder Seed', value: 'Pathfinder Seed' },
  { label: 'Pathfinder Pilot', value: 'Pathfinder Pilot' },
  { label: 'Pathfinder Makerspace', value: 'Pathfinder Makerspace' },
  { label: 'Other', value: 'Other' },
];

const statusItems = [
  { label: 'Pending', value: ProjectStatus.Pending },
  { label: 'Active', value: ProjectStatus.Active },
  { label: 'Archived', value: ProjectStatus.Archived },
  { label: 'Completed', value: ProjectStatus.Completed },
];
