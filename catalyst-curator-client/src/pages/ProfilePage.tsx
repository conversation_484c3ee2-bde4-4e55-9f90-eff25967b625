import { observer } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Tab, TabPanel } from '../lib';
import { Router } from '../platform/Router';
import { MainStackParamList } from '../routing/screens';
import { RoleNames } from '../services/codegen/types';
import { UserStore } from '../stores';
import { UsersAdminStore } from '../stores/UsersAdminStore';
import { AdvancedUserInformation } from './sections/user/AdvancedUserInformation';
import { UserInformation } from './sections/user/UserInformation';
import { UserManagement } from './sections/user/UserManagement';

export interface ProfilePageProps {
  userStore: UserStore;
  usersAdminStore: UsersAdminStore;
  router: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  location?: string;
}

export const ProfilePage = withTheme(
  observer(({ userStore, usersAdminStore, theme, location, router }: ProfilePageProps) => {
    const { styles, colors } = theme;
    const { components, margins } = styles;
    return (
      <View style={[components.flexAll, components.pageConstraints, margins.BottomL]}>
        {/* Austin decided to remove this for now...
         <Surface style={[components.minSectionStyle, borders.primary, { elevation: 1 }]}>
          <UserProfileBadge
            {...{
              firstName: userStore.user?.firstName,
              lastName: userStore.user?.lastName,
              subtitle: 'Innovation Officer',
              style: components.elementStyle,
              avatarStyle: StyleSheet.flatten([{ backgroundColor: colors.accent }, margins.RightM]),
              textStyle: { ...fonts.large, fontWeight: 'bold', color: colors.text },
              subtitleTextStyle: { ...fonts.medium, color: colors.text },
              avatarTextSize: 55,
            }}
          />
        </Surface>
          */}
        <TabPanel
          initialActiveId={location}
          tabs={getTabs(userStore, usersAdminStore)}
          onTabSelected={(tabId) => {
            router.replace('profile', { location: tabId as never });
          }}
        />
      </View>
    );
  }),
);

const getTabs = (userStore: UserStore, usersAdminStore: UsersAdminStore): Tab[] => {
  const adminDisabled = !userStore.user?.roles?.some((role) => role.name === RoleNames.Admin);
  return [
    { title: 'Basic Information', id: 'basic', element: <UserInformation {...{ userStore }} />, scrollable: true },
    {
      title: 'Advanced Settings',
      id: 'advanced',
      element: <AdvancedUserInformation {...{ userStore }} />,
      scrollable: true,
    },
    {
      title: 'User Management',
      id: 'manage',
      element: <UserManagement {...{ usersAdminStore }} />,
      disabled: adminDisabled,
      scrollable: true,
    },
  ];
};
