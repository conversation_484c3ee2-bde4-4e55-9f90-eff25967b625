import { Text, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { OpportunityListStore, OpportunityReportsStore, TenantStore, UserStore } from '../../stores';
import { MainStackParamList } from '../../routing/screens';
import { Router } from '../../platform/Router';
import { Button, Dates, Tooltip } from '../../lib';
import { HiddenView } from '../../lib/ui/atoms/HiddenView';
import { ClickableText } from '../../lib/ui/atoms/ClickableText';
import { ApplicationMetaStore } from '../../stores/ApplicationMetaStore';
import {
  Category,
  IsTiCloeType,
  Opportunity,
  OpportunityVisibility,
  OwnershipStatus,
  SearchOperator,
} from '../../services/codegen/types';
import { ColDef, ValueGetterParams } from 'ag-grid-community';
import { Priority } from '../../lib/Priority';
import { Icon } from '../../lib/ui/atoms/Icon';
import { IconLabel } from '../../lib/ui/atoms/IconLabel';
import { GridControl } from '../../appComponents/web/Grid.web';
import { GenericGrid } from '../../appComponents/GenericGrid';
import { PRIORITY_LABELS } from '../../lib/ui/organisms/PriorityMenu';
import { FilterInfoStore } from '../../stores/FilterInfoStore';
import { Checkbox } from '../../lib/ui/atoms/Checkbox';
import { useLocalObservable } from 'mobx-react';
import { AnalyticsHeader } from '../../appComponents/header/AnalyticsHeader';
import getHeaderConfig from '../../routing/analytics/getHeaderConfig';
import { AnalyticsStackParamList } from '../../routing/analytics/screens';
import { Route } from '@react-navigation/native';
import { ResourcesStore } from '../../stores/ResourcesStore';

interface TablePageProps {
  opportunityListStore: OpportunityListStore;
  mainStackRouter: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  tenantStore: TenantStore;
  eventFilterInfo: FilterInfoStore;
  router: Router<AnalyticsStackParamList>;
  resourcesStore: ResourcesStore;
  opportunityReportsStore: OpportunityReportsStore;
  route: Route<string>;
}

export const TablePage = withTheme(
  ({
    theme,
    opportunityListStore,
    mainStackRouter,
    userStore,
    tenantStore,
    eventFilterInfo,
    route,
    router,
    opportunityReportsStore,
    resourcesStore,
  }: TablePageProps) => {
    const {
      styles: { components, margins },
    } = theme;
    const { userMetaStore } = userStore;

    const localStore = useLocalObservable(() => ({
      gridControl: {} as GridControl,
      setGridControl(value: GridControl) {
        this.gridControl = value;
      },
    }));

    const colDefsMap = getColDefsMap(theme, tenantStore, opportunityListStore);

    const appHeaderProps = getHeaderConfig(route.name as keyof AnalyticsStackParamList);

    function setEventFilterInfo() {
      const filterStores = opportunityListStore.listFilterStores;
      const eventsCheckboxes = filterStores.campaignInfoFilterStore.info;
      if (location.pathname.includes('overview')) {
        eventsCheckboxes.splice(0, 1);
        const noneIndex = eventsCheckboxes.findIndex((info) => info.label === 'None');
        if (noneIndex !== -1) {
          eventsCheckboxes.splice(noneIndex, 1);
        }
      }
      return eventsCheckboxes;
    }

    return (
      <View style={[components.flexAll, margins.BottomL]}>
        <AnalyticsHeader
          {...appHeaderProps}
          router={router}
          userStore={userStore}
          resourcesStore={resourcesStore}
          opportunityReportsStore={opportunityReportsStore}
          tenantStore={tenantStore}
          setEventFilterInfo={setEventFilterInfo}
        />
        <View style={[components.flexAll, components.panelStyle, components.shadow]}>
          <View style={[{ flexDirection: 'row', alignSelf: 'flex-end' }]}>
            <HiddenView
              getVisible={() =>
                opportunityListStore.searchGroups.asArray().length !== 0 ||
                !opportunityListStore.searchFields.isDefault()
              }
            >
              <ClickableText
                style={[components.smallClickableTextStyle, margins.RightML]}
                onPress={() => handleResetFilters(opportunityListStore)}
              >
                RESET FILTERS
              </ClickableText>
            </HiddenView>
            <HiddenView getVisible={() => !!userMetaStore.anaTable?.hasHidden}>
              <ClickableText
                style={[components.smallClickableTextStyle, margins.RightML]}
                onPress={() => handleRestoreHidden(userStore, userMetaStore, localStore)}
              >
                SHOW HIDDEN
              </ClickableText>
            </HiddenView>
            <ClickableText style={[components.smallClickableTextStyle]} onPress={() => handleResetTable(userStore)}>
              RESET TABLE
            </ClickableText>
          </View>
          <GenericGrid<Opportunity>
            theme={theme}
            store={opportunityListStore}
            router={mainStackRouter}
            userStore={userStore}
            colDefsMap={colDefsMap}
            sortableCols={sortableCols}
            onRowPress={() => {}}
            getGridControl={() => localStore.gridControl}
            onGridReady={(gridControl: GridControl) => {
              localStore.gridControl = gridControl;
            }}
            table={userMetaStore.anaTable}
          />
        </View>
      </View>
    );
  },
);

const handleResetTable = (userStore: UserStore) => {
  userStore.resetAnaTableMeta();
};

const handleResetFilters = (store: OpportunityListStore) => {
  store.resetSearchValues();
  store.queryItems();
};

const handleRestoreHidden = (userStore: UserStore, userMetaStore: ApplicationMetaStore, locals: any) => {
  userMetaStore.anaTable?.showAllColumns();
  locals.gridControl?.showAllColumns();
  userStore.saveUserApplicationMeta();
};

const sortableCols = [
  'priority',
  'status',
  'title',
  'lastCurated',
  'createdAt',
  'org1',
  'solutionPathway',
  'statusNotes',
  'campaign',
  'armyModernizationPriority',
  'echelonApplicability',
  'transitionInContactLineOfEffort',
  'operationalRoles',
  'capabilityArea',
  'relatedOpportunityCount',
  'tenant.label',
  'function',
  'visibility',
  'isTiCLOE',
  'materielSolutionType',
  'DOTMLPFPPChange',
  'capabilitySponsor',
  'armyCapabilityManager',
  'context',
  'statement',
];

export interface GridColDef extends ColDef {}

// Note the width values here are defaults and are overridden by the defaults in tenantStore or the tenant's metadata (or the user's metadata)
const getColDefsMap = (
  theme: ReactNativePaper.ThemeProp,
  tenantStore: TenantStore,
  opportunityListStore: OpportunityListStore,
): {
  [key: string]: ColDef;
} => {
  const filterStores = opportunityListStore.listFilterStores;
  const {
    warFightingFunctionFilterStore,
    campaignInfoFilterStore,
    priorityFilterInfo,
    statusFilterInfo,
    relationshipsFilterInfo,
    capabilityAreaFilterInfo,
    operationalRolesFilterInfo,
    echelonApplicabilityFilterInfo,
    armyModernizationPriorityFilterInfo,
    isTiCLOEFilterInfo,
    materielSolutionTypeFilterInfo,
    DOTMLPFPPChangeFilterInfo,
    capabilitySponsorFilterInfo,
    armyCapabilityManagerFilterInfo,
  } = filterStores;
  const warFightingFunctionFilterInfo = warFightingFunctionFilterStore;
  warFightingFunctionFilterInfo.info = tenantStore.tenantConfig.fields?.opportunity.function?.values
    ? [
        {
          label: 'Unassigned',
          searchField: { fieldNames: ['function'], searchValue: null, operator: SearchOperator.Eq },
        },
        ...tenantStore.tenantConfig.fields?.opportunity.function.values?.map((functionVal) => ({
          label: functionVal.label,
          searchField: { fieldNames: ['function'], searchValue: functionVal.label, operator: SearchOperator.Match },
        })),
      ]
    : [];

  const {
    fontSizes,
    colors,
    styles: { fonts, components, margins, paddings },
  } = theme;
  return {
    tenant: {
      field: 'tenant',
      headerName: 'Portfolio',
      headerComponentParams: {
        sortPath: 'label',
      },
      autoHeight: true,
      cellRenderer: (params: any) => {
        return <Text style={[fonts.medium, { textAlign: 'center' }]}>{params.data.tenant.label}</Text>;
      },
    },
    priority: {
      field: 'priority',
      headerName: 'Priority',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const priority = params.data?.priority || Priority.NONE;
        return Priority[priority].toLocaleLowerCase();
      },
      cellStyle: { textTransform: 'capitalize' },
      headerComponentParams: {
        filterInfo: priorityFilterInfo,
      },
    },
    status: {
      field: 'status',
      headerName: 'Status',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const status = params.data?.status;
        //capitalize first letter of each word
        return status
          ?.replace('_', ' ')
          .toLocaleLowerCase()
          .replace(/\b\w/g, (c) => c.toUpperCase());
      },
      headerComponentParams: {
        filterInfo: statusFilterInfo,
      },
    },
    relatedOpportunityCount: {
      field: 'relatedOpportunityCount',
      headerName: 'Relationships',
      headerTooltip: 'Relationships',
      headerComponentParams: {
        headerRenderer: () => (
          <View>
            <Icon name="file-tree" size={23} style={[{ alignSelf: 'center' }]} />
          </View>
        ),
        filterInfo: relationshipsFilterInfo,
      },
      cellStyle: { alignItems: 'flex-start', display: 'flex', justifyContent: 'center' },
      cellRenderer: (params: any) => {
        const { relatedOpportunityCount } = params.data;

        return (
          <IconLabel
            style={[
              components.buttonSecondaryStyle,
              margins.None,
              paddings.RightMS,
              paddings.VerticalS,
              { width: 50, backgroundColor: '#E2E2E2', borderColor: '#E2E2E2' },
            ]}
            textStyle={[components.buttonSecondaryTextStyle, { color: theme.colors.text }]}
          >
            {relatedOpportunityCount}
          </IconLabel>
        );
      },
      onCellClicked: () => {},
      autoHeight: true,
    },
    title: {
      field: 'title',
      headerName: 'Problem Title',
      autoHeight: true,
      wrapText: true,
    },
    function: {
      field: 'function',
      headerName: tenantStore.tenantConfig.fields?.opportunity.function?.fieldLabel,
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: warFightingFunctionFilterInfo,
      },
    },
    visibility: {
      field: 'visibility',
      headerName: 'Private',
      cellStyle: { display: 'flex', justifyContent: 'center', alignItems: 'flex-start' },
      onCellClicked: () => {},
      cellRenderer: (params: any) => {
        return (
          <Checkbox
            getChecked={() => params.data?.visibility === OpportunityVisibility.Private}
            onChecked={() => {}}
            onUnchecked={() => {}}
            getDisabled={() => true}
            label={null}
            style={[{ alignSelf: 'center' }]}
          />
        );
      },
      headerComponentParams: {
        headerRenderer: () => (
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'flex-start',
              gap: 4,
            }}
          >
            <Text style={[components.dataTableHeaderTextStyle, fontSizes.small, { alignSelf: 'center' }]}>Private</Text>
            <Tooltip
              offsetY={-329}
              useTopPointer={true}
              text={`By selecting the Private button, you opt to keep the submission and related opportunity data at your unit level for further review and development. \nYour higher headquarters and other Army organizations will be unable to view the submission and opportunity until you opt out of Private mode.`}
            >
              <Icon name="help-circle" color={colors.textSecondary} size={20} />
            </Tooltip>
          </View>
        ),
      },
    },
    campaign: {
      field: 'campaign',
      wrapText: true,
      headerName: tenantStore.tenantConfig.fields?.opportunity?.campaign?.fieldLabel
        ? tenantStore.tenantConfig.fields.opportunity.campaign.fieldLabel
        : 'Event',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return params.data?.campaign;
      },
      headerComponentParams: {
        filterInfo: campaignInfoFilterStore,
      },
      autoHeight: true,
    },
    lastCurated: {
      field: 'lastCurated',
      headerName: 'Last Curated',
      cellRenderer: (params: any) => {
        if (!params.data?.curationInfo?.lastCurated) {
          return (
            <View style={{ justifyContent: 'center', alignItems: 'center', display: 'flex' }}>
              <View
                style={[
                  components.auxButton3Style,
                  components.buttonCompactStyle,
                  margins.None,
                  { width: 70, backgroundColor: '#676D79', borderWidth: 0, alignItems: 'center' },
                ]}
              >
                <Text style={[components.auxButton3TextStyle, components.buttonCompactStyle, { borderWidth: 0 }]}>
                  New
                </Text>
              </View>
            </View>
          );
        }

        return Dates.asSimpleDateString(params.data?.lastCurated);
      },
    },
    createdAt: {
      field: 'createdAt',
      headerName: 'Created',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return Dates.asSimpleDateString(params.data?.createdAt);
      },
    },
    org1: {
      field: 'org1',
      headerName: 'Org / Team',
      autoHeight: true,
      wrapText: true,
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const org1 = params.data?.org1;
        const org2 = params.data?.org2;
        return org1 || org2 ? `${org1 || 'N/A'}${org2 ? ' / ' + org2 : ''}` : 'N/A';
      },
    },
    stakeholders: {
      field: 'stakeholders',
      headerName: 'Stakeholders',
      autoHeight: true,
      wrapText: true,

      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const stakeholders = params.data?.stakeholders;

        if (!stakeholders?.length) return '';

        return stakeholders
          .map((s) => {
            const nameParts = [s.firstName, s.lastName].filter(Boolean);
            const name = nameParts.join(' ').trim();
            const org = s.org?.trim();

            if (!name && !org) return null;
            if (name && org) return `${name}, ${org}`;
            return name || org;
          })
          .filter(Boolean)
          .join('; ');
      },
    },
    armyModernizationPriority: {
      field: 'armyModernizationPriority',
      headerName: 'Army Modernizations',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: armyModernizationPriorityFilterInfo,
      },
    },
    echelonApplicability: {
      field: 'echelonApplicability',
      headerName: 'Echelon Applicability',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: echelonApplicabilityFilterInfo,
      },
    },
    context: {
      field: 'context',
      headerName: 'Problem Context',
      autoHeight: true,
      wrapText: true,
    },
    statement: {
      field: 'statement',
      headerName: 'Problem Statement',
      autoHeight: true,
      wrapText: true,
    },
    isTiCLOE: {
      field: 'isTiCLOE',
      headerName: 'TiC LOE',
      autoHeight: true,
      wrapText: true,
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        if (params.data?.isTiCLOE === IsTiCloeType.Yes) {
          return params.data.transitionInContactLineOfEffort || 'Yes';
        }
        if (params.data?.isTiCLOE === IsTiCloeType.No) {
          return 'No';
        }
        return undefined;
      },
      headerComponentParams: {
        filterInfo: isTiCLOEFilterInfo,
      },
    },
    operationalRoles: {
      field: 'operationalRoles',
      headerName: 'Operational Roles',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: operationalRolesFilterInfo,
      },
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const operationalRoles = params.data?.operationalRoles;
        //if there is more than 1 role, abbreviate the first role
        if (operationalRoles && operationalRoles.length > 1) {
          if (operationalRoles[0] === 'Combat Service Support') operationalRoles[0] = 'CSS';
          if (operationalRoles[0] === 'Combat Support') operationalRoles[0] = 'CS';
          if (operationalRoles[0] === 'Combat Arms') operationalRoles[0] = 'CA';
        }
        return operationalRoles?.join(' > ');
      },
    },
    DOTMLPFPPChange: {
      field: 'DOTMLPFPPChange',
      headerName: 'DOTMLPF-P Domains Impacted',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: DOTMLPFPPChangeFilterInfo,
      },
    },
    capabilitySponsor: {
      field: 'capabilitySponsor',
      headerName: 'Capability Sponsor',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: capabilitySponsorFilterInfo,
      },
    },
    armyCapabilityManager: {
      field: 'armyCapabilityManager',
      headerName: 'Army Capability Manager',
      autoHeight: true,
      wrapText: true,
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        if (params.data?.armyCapabilityManager) {
          return params.data?.armyCapabilityManager[params.data?.armyCapabilityManager.length - 1];
        }
      },
      headerComponentParams: {
        filterInfo: armyCapabilityManagerFilterInfo,
      },
    },
    opportunityOwnerStatuses: {
      field: 'opportunityOwnerStatuses',
      headerName: 'Opportunity Owner',
      autoHeight: true,
      wrapText: true,
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const owners = params.data?.opportunityOwnerStatuses;

        const currentOwner = owners?.find((status) => status.status === OwnershipStatus.Current);
        if (currentOwner) {
          const user = currentOwner.owner?.user;
          const name = `${user?.firstName} ${user?.lastName}`;
          const org = [user?.org1, user?.org2, user?.org3, user?.org4].filter(Boolean).join(' ');
          return `${name} - ${org}`.trim();
        }
        return '';
      },
    },
    capabilityArea: {
      field: 'capabilityArea',
      headerName: 'Capability Area',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: capabilityAreaFilterInfo,
      },
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        if (params.data?.capabilityArea) {
          return params.data?.capabilityArea.join(', ');
        }
      },
    },
    categories: {
      field: 'categories',
      headerName: 'Custom Tags',
      autoHeight: true,
      wrapText: true,
      cellRenderer: (params: any) => {
        return (
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', display: 'flex', gap: 8 }}>
            {params.data?.categories.map((tag: Category) => {
              return (
                <Button
                  key={tag.id}
                  compact={true}
                  style={{ backgroundColor: colors.secondaryPrimaryColor }}
                  labelStyle={[{ color: colors.text, fontSize: 14, textTransform: 'capitalize' }, fonts.regular]}
                >
                  {tag.name}
                </Button>
              );
            })}
          </View>
        );
      },
    },
    attachments: {
      field: 'attachments',
      headerName: 'Attachments',
      autoHeight: true,
      wrapText: true,
      cellStyle: { display: 'flex', justifyContent: 'center', alignItems: 'flex-start' },
      cellRenderer: (params: any) => {
        if (!params.data) return null;
        const { attachments, links } = params.data;
        const attachmentsNum = attachments.length;
        const linksNum = links.length;

        function getText() {
          if (linksNum === 0 && attachmentsNum === 0) return '0';
          return `${attachmentsNum} / ${linksNum}`;
        }
        return (
          <IconLabel
            style={[
              components.buttonSecondaryStyle,
              margins.None,
              paddings.RightMS,
              paddings.VerticalS,

              { backgroundColor: theme.colors.accent, borderWidth: 0 },
              { minWidth: 50 },
            ]}
            textStyle={components.buttonSecondaryTextStyle}
          >
            {getText()}
          </IconLabel>
        );
      },
    },
    solutionPathway: {
      field: 'solutionPathway',
      headerName: 'Solution Pathway',
      autoHeight: true,
      wrapText: true,
    },
    statusNotes: {
      field: 'statusNotes',
      headerName: 'Status Notes',
      autoHeight: true,
      wrapText: true,
    },
    materielSolutionType: {
      field: 'materielSolutionType',
      headerName: 'Materiel Solution Type',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: materielSolutionTypeFilterInfo,
      },
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        if (params.data?.materielSolutionType && params.data.solutionConcepts) {
          return params.data?.materielSolutionType + ' - ' + params.data.solutionConcepts;
        } else if (params.data?.materielSolutionType) {
          return params.data?.materielSolutionType;
        }
      },
    },
  } as Record<string, any>;
};
