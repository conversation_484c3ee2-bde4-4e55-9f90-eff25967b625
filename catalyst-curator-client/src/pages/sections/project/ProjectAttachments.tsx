import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { LinksSection } from '../../../appComponents/LinksSection';
import { AttachmentInfo } from '../../../lib/ui/molecules/FileAttachments';
import { ProjectStore } from '../../../stores/ProjectStore';
import { openWebFileLink } from '../../../utilities/File';
import { Attachment, Location } from '../../../services/codegen/types';
import { UserStore } from '../../../stores';
import { View } from 'react-native';
import { AttachmentsSection } from '../../../appComponents/AttachmentsSection';
import { Group } from '../../../lib/ui/atoms/Group';

interface ProjectAttachmentsProps {
  theme: ReactNativePaper.ThemeProp;
  projectStore: ProjectStore;
  userStore: UserStore;
}

export const ProjectAttachments = withTheme(
  observer(({ theme, projectStore, userStore }: ProjectAttachmentsProps) => {
    return (
      <Group title="Attachments" style={{ zIndex: 1 }}>
        <AttachmentsSection
          theme={theme}
          userStore={userStore}
          title="Attachments"
          attachments={(projectStore.project?.attachments as AttachmentInfo[]) || []}
          isUploadInProgress={projectStore.fileUploadInProgress}
          onRetrieveAttachment={(id) => handleOnRetrieveDocument(projectStore, id)}
          onDeleteAttachment={(id) => handleOnDeleteDocument(projectStore, id)}
          onAddAttachment={async (result) => handleOnAddDocument(result, projectStore)}
          onUpdateAttachment={async (id, displayName, notes) => projectStore.updateAttachment(id, displayName, notes)}
          editable={true}
        />
        <LinksSection
          theme={theme}
          userStore={userStore}
          editable={true}
          links={projectStore.project?.links || []}
          onAddLink={(url, name, notes) => handleOnAddLink(projectStore, url, name, notes)}
          onDeleteLink={(id) => handleOnDeleteLink(projectStore, id)}
          onUpdateLink={(id, url, name, notes) => handleOnUpdateLink(projectStore, id, url, name, notes)}
        />
      </Group>
    );
  }),
);

async function handleOnAddLink(projectStore: ProjectStore, url: string, name: string, notes: string) {
  await projectStore.addLink(url, name, notes);
}

async function handleOnUpdateLink(
  projectStore: ProjectStore,
  linkId: string,
  url: string,
  name: string,
  notes: string,
) {
  await projectStore.updateLink(linkId, url, name, notes);
}

async function handleOnDeleteLink(projectStore: ProjectStore, linkId: string) {
  await projectStore.deleteLink(linkId);
}

const handleOnAddDocument = async (
  result: { file: File; uri: string; displayName?: string; notes?: string },
  projectStore: ProjectStore,
): Promise<void> => {
  await projectStore.addAttachment(result);
};

const handleOnDeleteDocument = (projectStore: ProjectStore, attachmentId: string): void => {
  projectStore.deleteAttachment(attachmentId);
};

const handleOnRetrieveDocument = (projectStore: ProjectStore, attachmentId: string): void => {
  const attachment = projectStore.project?.attachments.find((a: Attachment) => a.id === attachmentId);
  projectStore.getAttachment(attachmentId).then((loc: Location) => {
    return openWebFileLink(loc.location, attachment?.name || '', attachment?.mimetype || '');
  });
};
