import { observer } from 'mobx-react';
import React from 'react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { roleNameSets, RolesInfo } from '../../../lib';
import { HelperTextProps } from '../../../lib';
import { LabeledPhoneInput } from '../../../lib';
import { LabeledTextInput } from '../../../lib';
import { UserRoleMenu } from '../../../lib';
import { CuratorStores } from '../../../platform/initializers';
import { StoresProvider } from '../../../lib/stores/StoresProvider';
import { UserLinks } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import {
  getCountryCodeItem,
  getCountryCodeItems,
  onSelectCountryCode,
  setPhoneNumber,
  stripCountryCode,
} from '../../../lib/phoneNumberUtils';
import { LabeledDependentDropdown } from '../../../lib/ui/molecules/LabeledDependentDropdown';

interface EditUserBasicProps {
  theme: ReactNativePaper.ThemeProp;
  userAdminStore: UserAdminStore;
  includeUserId?: boolean;
  onUpdateUser: (userAdminStore: UserAdminStore, links?: UserLinks) => void;
}

export const EditUserBasic = withTheme(
  observer(({ userAdminStore, onUpdateUser, includeUserId, theme }: EditUserBasicProps) => {
    const { styles } = theme;
    const { margins } = styles;
    const organizationStore = StoresProvider.get<CuratorStores>().getStore('OrganizationStore');

    return (
      <>
        {includeUserId && (
          <View style={{ flexDirection: 'row' }}>
            <LabeledTextInput
              style={[margins.RightXL]}
              textInputProps={{
                multiline: false,
                spellcheck: false,
                numberOfLines: 1,
                getValue: () => getValue(userAdminStore, 'emailAddress'),
                setValue: (value) => setValue(userAdminStore, 'emailAddress', value),
                onDebounceValue: () => handleValueUpdate(userAdminStore, onUpdateUser),
              }}
              labelText="Email Address"
              getHelperTextProps={() => getHelperTextProps('emailAddress', userAdminStore)}
            />
          </View>
        )}
        <View style={{ flexDirection: 'row' }}>
          <LabeledTextInput
            style={[margins.RightXL]}
            textInputProps={{
              multiline: false,
              spellcheck: false,
              numberOfLines: 1,
              getValue: () => getValue(userAdminStore, 'firstName'),
              setValue: (value) => setValue(userAdminStore, 'firstName', value),
              onDebounceValue: () => handleValueUpdate(userAdminStore, onUpdateUser),
            }}
            labelText="First name"
            getHelperTextProps={() => getHelperTextProps('firstName', userAdminStore)}
          />
          <LabeledTextInput
            textInputProps={{
              multiline: false,
              spellcheck: false,
              getValue: () => getValue(userAdminStore, 'lastName'),
              setValue: (value) => setValue(userAdminStore, 'lastName', value),
              onDebounceValue: () => handleValueUpdate(userAdminStore, onUpdateUser),
            }}
            labelText="Last name"
            getHelperTextProps={() => getHelperTextProps('lastName', userAdminStore)}
          />
        </View>
        {organizationStore.organizations.length > 0 &&
          (() => {
            const orgValue1 = getValue(userAdminStore, 'org1');
            const orgValue2 = getValue(userAdminStore, 'org2');
            const orgValue3 = getValue(userAdminStore, 'org3');
            const orgValue4 = getValue(userAdminStore, 'org4');
            return (
              <>
                <View style={[{ flexDirection: 'column' }, margins.BottomL]}>
                  <LabeledDependentDropdown
                    labelText="Team"
                    getHelperTextProps={() => getHelperTextProps('org1', userAdminStore)}
                    dropdownMenuProps={{
                      theme,
                      defaultValues: [
                        {
                          label: 'Team',
                          value: orgValue1 && { label: orgValue1, value: orgValue1 },
                          fieldName: 'org1',
                        },
                        {
                          label: undefined,
                          value: orgValue2 && { label: orgValue2, value: orgValue2 },
                          fieldName: 'org2',
                        },
                        {
                          label: undefined,
                          value: orgValue3 && { label: orgValue3, value: orgValue3 },
                          fieldName: 'org3',
                        },
                        {
                          label: undefined,
                          value: orgValue4 && { label: orgValue4, value: orgValue4 },
                          fieldName: 'org4',
                        },
                      ],
                      getMenuGroups: () => organizationStore.orgsToMenuGroups(),
                      onItemSelected: (item, fieldName) => {
                        setValue(userAdminStore, fieldName, item?.label);
                        handleValueUpdate(userAdminStore, onUpdateUser);
                      },
                    }}
                  />
                </View>
              </>
            );
          })()}
        <View style={{ flexDirection: 'row' }}>
          <LabeledPhoneInput
            style={[margins.RightXL]}
            countryCodeMenuProps={{
              getMenuItems: () => getCountryCodeItems(),
              getValue: () => getCountryCodeItem(userAdminStore.getValue('phone')),
              onItemSelected: (item) => {
                onSelectCountryCode(
                  item,
                  userAdminStore.setValue.bind(userAdminStore),
                  userAdminStore.getValue('phone'),
                );
                const phoneNumber = userAdminStore.getValue('phone');
                const phoneNumberNoCode = stripCountryCode(phoneNumber);
                if (phoneNumberNoCode !== '') {
                  handleValueUpdate(userAdminStore, onUpdateUser);
                }
              },
              getEditable: () => true,
              anchorStyle: { height: 41, maxHeight: 41 },
            }}
            textInputProps={{
              multiline: false,
              spellcheck: false,
              getValue: () => stripCountryCode(userAdminStore.getValue('phone')),
              setValue: (value) =>
                setPhoneNumber(value, userAdminStore.setValue.bind(userAdminStore), userAdminStore.getValue('phone')),
              placeholder: '(*************',
              onDebounceValue: () => handleValueUpdate(userAdminStore, onUpdateUser),
              autoComplete: 'new-password',
            }}
            labelText="Phone"
            getHelperTextProps={() => getHelperTextProps('phone', userAdminStore)}
          />
          <UserRoleMenu
            value={userAdminStore.rolesInfo}
            labelText="Select Role:"
            onValueSelected={(menuItem) => handleChangeRole(userAdminStore, menuItem.value, onUpdateUser)}
            dropdownMenuProps={{ getEditable: () => true }}
          />
        </View>
      </>
    );
  }),
);

const getHelperTextProps = (name: string, userAdminStore: UserAdminStore): HelperTextProps => {
  return { type: 'error', children: userAdminStore.getPropertyError(name) };
};

const setValue = (userAdminStore: UserAdminStore, name: string, value: any) => {
  userAdminStore.setValue(name, value);
};

const getValue = (userAdminStore: UserAdminStore, name: string) => {
  return userAdminStore.getValue(name, '');
};

const handleChangeRole = (
  userAdminStore: UserAdminStore,
  rolesInfo: RolesInfo,
  onUpdateUser: (userAdminStore: UserAdminStore, links?: UserLinks) => void,
) => {
  onUpdateUser(userAdminStore, { roleNames: roleNameSets[rolesInfo.effectiveRoleName] });
};

const handleValueUpdate = (userAdminStore: UserAdminStore, onUpdateUser: (userAdminStore: UserAdminStore) => void) => {
  onUpdateUser(userAdminStore);
};
