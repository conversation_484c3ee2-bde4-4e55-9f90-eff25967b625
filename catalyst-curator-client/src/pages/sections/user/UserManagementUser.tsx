import { runInAction } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import { StyleProp, TextStyle, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { RolesInfo } from '../../../lib';
import { Button } from '../../../lib';
import { Label } from '../../../lib';
import { UserProfileBadge } from '../../../lib';
import { AnimatedView } from '../../../lib/ui/atoms/AnimatedView';
import { AccountState, RoleNames, User, UserLinks, VerifiedStatus } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import { UserManagementEditUser } from './UserManagementEditUser';
import { IconButton } from '../../../lib/ui/atoms/IconButton';
import { UserManagementUnlockDialog } from './UserManagementUnlockDialog';

interface UserManagementProps {
  index: number;
  userProp: User;
  onUpdateUser: (userAdminStore: UserAdminStore, links?: UserLinks) => void;
  onDeleteUser: (userAdminStore: UserAdminStore) => void;
  onUnlockUser?: (userAdminStore: UserAdminStore) => void;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  editing: boolean;
  setEditing: (value: boolean) => void;
  isUserUnlockDialogOpen: boolean;
  setIsUserUnlockDialogOpen(value: boolean): void;
}

export const UserManagementUser = withTheme(
  observer(({ onUpdateUser, userProp, onDeleteUser, onUnlockUser, theme, index }: UserManagementProps) => {
    const { styles, colors, fontSizes } = theme;
    const { components, margins, paddings } = styles;
    const userAdminStore = new UserAdminStore(userProp);
    const localStore = useLocalObservable(() => ({
      editing: false,
      setEditing(value: boolean) {
        this.editing = value;
      },
      isUserUnlockDialogOpen: false,
      setIsUserUnlockDialogOpen(value: boolean) {
        this.isUserUnlockDialogOpen = value;
      },
    }));

    const { editing } = localStore;
    const { user, rolesInfo } = userAdminStore;

    // @TODO
    // this needs to be moved out to configuration per tenant
    const registeredStatus = user.status === VerifiedStatus.Unverified ? 'Unverified' : undefined;

    const rowBackground = index % 2 === 0 ? '#F4F4FA' : 'transparent';
    return (
      <View>
        <View
          style={[
            paddings.HorizontalML,
            paddings.VerticalM,
            {
              flexDirection: 'row',
              flexGrow: 1,
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: rowBackground,
            },
          ]}
        >
          <UserProfileBadge
            {...{ firstName: user.firstName, lastName: user.lastName }}
            style={{ flex: 1 }}
            avatarStyle={{ backgroundColor: colors.mediumGreen }}
          />
          <View style={{ flexDirection: 'row', justifyContent: 'center', flex: 1, alignItems: 'flex-end' }}>
            <Label textStyle={getRoleLabelStyle(theme, rolesInfo)}>{rolesInfo.displayName}</Label>
            {registeredStatus && (
              <Label textStyle={[fontSizes.xSmall, margins.LeftS]}>{` (${registeredStatus})`}</Label>
            )}
          </View>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'flex-end',
              flexDirection: 'row',
              gap: 24,
              width: 96,
            }}
          >
            {user.accountState === AccountState.Locked && !editing && (
              <IconButton
                icon="lock-outline"
                color="#676D79"
                onPress={() => localStore.setIsUserUnlockDialogOpen(true)}
              />
            )}
            {editing ? (
              <Button iconName={'check'} type={'primary'} onPress={() => onClickEdit(localStore, userAdminStore)}>
                Finish Editing
              </Button>
            ) : (
              <IconButton
                icon={'pencil'}
                color={'#676D79'}
                style={{
                  borderWidth: 1,
                  borderColor: '#C4C7CF',
                  paddingHorizontal: 8,
                  paddingVertical: 6,
                  borderRadius: 4,
                }}
                onPress={() => onClickEdit(localStore, userAdminStore)}
              />
            )}
          </View>
        </View>
        {editing && (
          <AnimatedView>
            <UserManagementEditUser
              userAdminStore={userAdminStore}
              onUpdateUser={onUpdateUser}
              onDeleteUser={onDeleteUser}
            />
          </AnimatedView>
        )}
        <UserManagementUnlockDialog
          getVisible={() => {
            return localStore.isUserUnlockDialogOpen;
          }}
          onCancel={() => localStore.setIsUserUnlockDialogOpen(false)}
          onConfirm={() => localStore.setIsUserUnlockDialogOpen(false)}
          onUnlock={() => {
            if (onUnlockUser) {
              onUnlockUser(userAdminStore);
            }
            localStore.setIsUserUnlockDialogOpen(false);
          }}
        />
      </View>
    );
  }),
);

const getRoleLabelStyle = (theme: ReactNativePaper.ThemeProp, rolesInfo: RolesInfo): StyleProp<TextStyle> => {
  const style: StyleProp<TextStyle> = theme.fontSizes.mediumSmall;
  if (rolesInfo.effectiveRoleName === RoleNames.Admin) {
    return [style, { color: theme.colors.error }];
  } else if (rolesInfo.effectiveRoleName === RoleNames.Curator) {
    return [style, { color: theme.colors.info }];
  }
  return style;
};

const onClickEdit = (localStore: Locals, userAdminStore: UserAdminStore) => {
  runInAction(() => {
    localStore.setEditing(!localStore.editing);
    if (!localStore.editing) userAdminStore.clearBuffer();
  });
};
