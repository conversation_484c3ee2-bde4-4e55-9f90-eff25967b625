import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import {
  Button,
  ConfirmationDialog,
  PasswordChangeForm,
  PasswordChangeFormState,
  Title,
  usePasswordChangeForm,
} from '../../../lib';
import { stripCountryCode } from '../../../lib/phoneNumberUtils';
import { UserLinks } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import { EditUserBasic } from './EditUserBasic';

interface UserManagementCreateUserProps {
  userAdminStore: UserAdminStore;
  theme: ReactNativePaper.ThemeProp;
  onUserCreated: (userAdminStore: UserAdminStore) => void;
  onCancel: () => void;
}
interface Locals {
  confirmUserCreatedVisible: boolean;
  setConfirmUserCreatedVisible: (value: boolean) => void;
}

export const UserManagementCreateUser = withTheme(
  observer((props: UserManagementCreateUserProps) => {
    const { userAdminStore, theme, onCancel } = props;
    const { styles, fonts, fontSizes } = theme;

    const passwordState = usePasswordChangeForm({ showCurrent: false });
    const { isPasswordValid } = passwordState;

    const localStore = useLocalObservable(() => ({
      confirmUserCreatedVisible: false,
      setConfirmUserCreatedVisible(value: boolean) {
        this.confirmUserCreatedVisible = value;
      },
    }));

    return (
      <View>
        <View
          style={[styles.components.sectionStyle, styles.borders.primary, styles.margins.ML, { alignItems: 'stretch' }]}
        >
          <View style={[styles.paddings.HorizontalL, { alignItems: 'stretch' }]}>
            <Title style={[fonts.mediumTitle, fontSizes.medium, styles.margins.BottomM]}>New User</Title>
            <EditUserBasic
              {...{
                theme,
                userAdminStore,
                includeUserId: true,
                onUpdateUser: (userAdminStore, links) => handleUpdateUser(userAdminStore, links),
              }}
            />
            <PasswordChangeForm passwordState={passwordState} editable={true} />
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 16 }}>
              <Button type="secondary" style={styles.margins.RightS} compact={true} onPress={onCancel}>
                Cancel
              </Button>
              <Button
                type="primary"
                getDisabled={() => !isPasswordValid || userAdminStore.hasErrors}
                compact={true}
                onPress={() => handleCreateUser(localStore, props, passwordState)}
              >
                Create User
              </Button>
            </View>
          </View>
        </View>
        <ConfirmationDialog
          title="User Created"
          message={`New user ${userAdminStore.user.firstName} ${userAdminStore.user.lastName} has been created successfully!`}
          getVisible={() => localStore.confirmUserCreatedVisible}
          onConfirm={() => handleUserCreated(localStore, props)}
          onCancel={() => localStore.setConfirmUserCreatedVisible(false)}
        />
      </View>
    );
  }),
);

const handleUserCreated = (localsStore: Locals, props: UserManagementCreateUserProps) => {
  localsStore.setConfirmUserCreatedVisible(false);
  props.onUserCreated(props.userAdminStore);
};

const handleCreateUser = async (
  localsStore: Locals,
  props: UserManagementCreateUserProps,
  { isPasswordValid, newPassword }: PasswordChangeFormState,
) => {
  const { onUserCreated: onCreateUser, userAdminStore } = props;
  const phoneNumber = userAdminStore.getValue('phone');
  const phoneNumberNoCode = stripCountryCode(phoneNumber);
  if (phoneNumber && phoneNumberNoCode === '') {
    userAdminStore.setValue('phone', null);
  }
  if (isPasswordValid && userAdminStore.validateAll()) {
    userAdminStore.setValue('password', newPassword);
    await userAdminStore.createUser();
    localsStore.setConfirmUserCreatedVisible(true);
  }
};

const handleUpdateUser = async (userAdminStore: UserAdminStore, links?: UserLinks) => {
  if (links) userAdminStore.pendingUserLinks = links;
};
