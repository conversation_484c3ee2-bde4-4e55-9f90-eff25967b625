import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { errorCodes, errorKeys } from '../../../constants/errorCodes';
import { Button, Message, PasswordChangeForm, PasswordChangeFormState, usePasswordChangeForm } from '../../../lib';
import { UserStore } from '../../../stores';

interface EditUserAdvancedProps {
  userStore: UserStore;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  editing: boolean;
  showSuccess: boolean;
  setEditing(editing: boolean): void;
  setShowSuccess(showSuccess: boolean): void;
}

export const EditUserAdvanced = withTheme(
  observer(({ userStore, theme }: EditUserAdvancedProps) => {
    const { fontSizes, styles } = theme;
    const { margins, paddings } = styles;

    const passwordState = usePasswordChangeForm({ showCurrent: true });

    const localStore = useLocalObservable(() => ({
      editing: false,
      showSuccess: false,
      setEditing(editing: boolean) {
        this.editing = editing;
      },
      setShowSuccess(showSuccess: boolean) {
        this.showSuccess = showSuccess;
      },
    }));

    return (
      <>
        <View style={{ flexDirection: 'row', alignSelf: 'flex-end' }}>
          {localStore.editing && (
            <>
              <Button type="secondary" style={margins.RightS} compact={true} onPress={() => handleCancel(localStore)}>
                Cancel
              </Button>
              <Button
                type="primary"
                getDisabled={() => !passwordState.isPasswordValid}
                compact={true}
                onPress={() => {
                  handleUpdatePassword(localStore, userStore, passwordState);
                }}
              >
                Update Password
              </Button>
            </>
          )}
          {!localStore.editing && (
            <Button type="secondary" compact={true} onPress={() => handleStartEditing(localStore)}>
              Change Password
            </Button>
          )}
        </View>
        <View style={[paddings.HorizontalXL]}>
          {localStore.showSuccess ? (
            <Message style={[fontSizes.mediumLarge]}>Password has been updated!</Message>
          ) : null}
          {!localStore.showSuccess ? (
            <PasswordChangeForm passwordState={passwordState} editable={localStore.editing} />
          ) : null}
        </View>
      </>
    );
  }),
);

const handleStartEditing = (localStore: Locals) => {
  localStore.setShowSuccess(false);
  localStore.setEditing(!localStore.editing);
};

const handleUpdatePassword = async (
  localStore: Locals,
  userStore: UserStore,
  { currentPassword, newPassword, isPasswordValid, tenantHandle }: PasswordChangeFormState,
) => {
  if (isPasswordValid) {
    try {
      await userStore.changePassword(tenantHandle, currentPassword, newPassword);
      localStore.setShowSuccess(true);
      localStore.setEditing(!localStore.editing);
    } catch (e) {
      localStore.setShowSuccess(false);
    }
  }
};

const handleCancel = (localStore: Locals) => {
  localStore.setEditing(!localStore.editing);
};
