import { observer, useLocalObservable } from 'mobx-react';
import { useEffect } from 'react';
import { FlatList } from 'react-native';
import { withTheme } from 'react-native-paper';
import { AccountState, User, UserLinks } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import { UsersAdminStore } from '../../../stores/UsersAdminStore';
import { UserManagementHeader } from './UserManagementHeader';
import { UserManagementUser } from './UserManagementUser';
import { UserManagementCreateUser } from './UserManangementCreateUser';

interface UserManagementProps {
  usersAdminStore: UsersAdminStore;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  showCreateUser: boolean;
  setShowCreateUser: (value: boolean) => void;
}

export const UserManagement = withTheme(
  observer(({ usersAdminStore }: UserManagementProps) => {
    useEffect(() => {
      usersAdminStore.queryUsers();
    }, [usersAdminStore]);

    const localStore = useLocalObservable(() => ({
      showCreateUser: false,
      setShowCreateUser(value: boolean) {
        this.showCreateUser = value;
      },
    }));

    const { users } = usersAdminStore;
    const header = (
      <UserManagementHeader usersAdminStore={usersAdminStore} onClickNewUser={() => handleToggleCreate(localStore)} />
    );

    return localStore.showCreateUser ? (
      <UserManagementCreateUser
        userAdminStore={new UserAdminStore({} as User)}
        onUserCreated={() => handleUserCreated(localStore, usersAdminStore)}
        onCancel={() => handleToggleCreate(localStore)}
      />
    ) : (
      <FlatList
        style={{ flexGrow: 1 }}
        ListHeaderComponent={header}
        stickyHeaderIndices={[0]}
        data={users}
        renderItem={({ index, item }) => renderItem({ index, item, usersAdminStore })}
        keyExtractor={(item) => item.id}
      />
    );
  }),
);

const renderItem = ({
  index: index,
  item: user,
  usersAdminStore,
}: {
  index: number;
  item: User;
  usersAdminStore: UsersAdminStore;
}) => {
  return (
    <UserManagementUser
      {...{
        index,
        userProp: user,
        onUpdateUser: (userAdminStore, links) => handleUpdateUser(userAdminStore, usersAdminStore, links),
        onDeleteUser: (userAdminStore) => handleDeleteUser(userAdminStore, usersAdminStore),
        onUnlockUser: (userAdminStore) => handleUnlockUser(userAdminStore, usersAdminStore),
      }}
    />
  );
};

const handleUserCreated = (localStore: Locals, usersAdminStore: UsersAdminStore) => {
  usersAdminStore.queryUsers();
  localStore.setShowCreateUser(false);
};

const handleUnlockUser = async (userAdminStore: UserAdminStore, usersAdminStore: UsersAdminStore) => {
  userAdminStore.setValue('accountState', AccountState.Unlocked);
  await userAdminStore.saveUser();
  await usersAdminStore.mergeUpdatedUser(userAdminStore.user);
};

const handleUpdateUser = async (
  userAdminStore: UserAdminStore,
  usersAdminStore: UsersAdminStore,
  links?: UserLinks,
) => {
  if (links) userAdminStore.pendingUserLinks = links;
  if (!userAdminStore.hasErrors) {
    await userAdminStore.saveUser();
    usersAdminStore.mergeUpdatedUser(userAdminStore.user);
  }
};

const handleDeleteUser = async (userAdminStore: UserAdminStore, usersAdminStore: UsersAdminStore) => {
  const id = userAdminStore.user.id;
  usersAdminStore.deleteUser(id);
};

const handleToggleCreate = (localStore: Locals) => {
  localStore.setShowCreateUser(!localStore.showCreateUser);
};
