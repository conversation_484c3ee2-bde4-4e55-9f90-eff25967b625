import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { Button, DialogProps, Title } from '../../../lib';
import { View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';

interface UserManagementUnlockDialogProps extends DialogProps {
  onConfirm: () => void;
  onCancel: () => void;
  onUnlock: () => void;
  theme: ReactNativePaper.ThemeProp;
}
export const UserManagementUnlockDialog = withTheme(
  ({ theme, onConfirm, onCancel, onUnlock, getVisible }: UserManagementUnlockDialogProps) => {
    const {
      styles: { margins, paddings, components, fontSizes, fonts },
      colors,
    } = theme;

    return (
      <ContentDialog
        {...{ onConfirm, theme }}
        noTitleBar={true}
        style={{ borderRadius: 16 }}
        contentStyle={[components.unlockUserDialogStyle, { borderRadius: 16 }]}
        getVisible={getVisible}
        showClose={true}
      >
        <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>Locked</Title>
        <View style={[{ flex: 1, gap: 16, alignSelf: 'flex-start', justifyContent: 'center' }]}>
          <Text style={{ ...components.messageInfoStyle }}>
            This user's account is locked. Would you like to unlock it?
          </Text>
        </View>
        <View style={[{ flex: 1, flexDirection: 'row', gap: 4, alignSelf: 'flex-end' }, margins.TopL]}>
          <Button mode={'text'} labelStyle={{ color: '#323438' }} onPress={() => onCancel()}>
            Cancel
          </Button>
          <Button
            type={'primary'}
            onPress={() => onUnlock()}
            labelStyle={[components.textInputReadOnlyStyle, { color: colors.textLight }]}
            style={{ backgroundColor: '#258750' }}
          >
            Unlock Account
          </Button>
        </View>
      </ContentDialog>
    );
  },
);
