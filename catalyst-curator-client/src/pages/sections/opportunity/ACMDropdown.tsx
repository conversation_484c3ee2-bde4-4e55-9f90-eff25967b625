import { observer, useLocalObservable } from 'mobx-react';
import { Pressable } from 'react-native';
import { withTheme } from 'react-native-paper';
import { MenuGroup } from '../../../lib';
import { OpportunityStore } from '../../../stores';
import { ACMDialog } from './ACMDialog';
import { LabeledDependentDropdown } from '../../../lib/ui/molecules/LabeledDependentDropdown';

interface ACMDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const ACM_OPTIONS_BY_CDID: Record<string, MenuGroup[]> = {
  'Directorate of Concepts': [
    {
      item: { label: 'Army Concepts Division', value: 'Army Concepts Division' },
      children: [
        {
          item: { label: 'Army Concepts Division (No Subcategory)', value: 'Army Concepts Division (No Subcategory)' },
        },
        { item: { label: 'Future Concepts Branch', value: 'Future Concepts Branch' } },
        { item: { label: 'Emerging Concepts Branch', value: 'Emerging Concepts Branch' } },
        { item: { label: 'Technology to Concepts Branch', value: 'Technology to Concepts Branch' } },
        { item: { label: 'Disruptive Technology Branch', value: 'Disruptive Technology Branch' } },
        { item: { label: 'Support', value: 'Support' } },
      ],
    },
    {
      item: { label: 'Future Warfare Division', value: 'Future Warfare Division' },
      children: [
        {
          item: {
            label: 'Future Warfare Division (No Subcategory)',
            value: 'Future Warfare Division (No Subcategory)',
          },
        },
        { item: { label: 'Army Wargaming Branch', value: 'Army Wargaming Branch' } },
        { item: { label: 'Integration & Analysis Branch', value: 'Integration & Analysis Branch' } },
        { item: { label: 'Support', value: 'Support' } },
      ],
    },
    {
      item: { label: 'Joint Concepts Division', value: 'Joint Concepts Division' },
      children: [
        {
          item: {
            label: 'Joint Concepts Division (No Subcategory)',
            value: 'Joint Concepts Division (No Subcategory)',
          },
        },
        { item: { label: 'Joint Concepts Branch', value: 'Joint Concepts Branch' } },
        { item: { label: 'Joint Wargaming Branch', value: 'Joint Wargaming Branch' } },
        { item: { label: 'Support', value: 'Support' } },
      ],
    },
  ],
  'FCC Futures Integration Directorate': [
    {
      item: {
        label: 'Architecture Integration Management Division (AIMD)',
        value: 'Architecture Integration Management Division (AIMD)',
      },
    },
    { item: { label: 'Combat Systems Integration Division', value: 'Combat Systems Integration Division' } },
    { item: { label: 'Capability Developments Division', value: 'Capability Developments Division' } },
    { item: { label: 'Fires', value: 'Fires' } },
    {
      item: {
        label: 'Maneuver, Aviation & Soldier Division (MASD)',
        value: 'Maneuver, Aviation & Soldier Division (MASD)',
      },
    },
    {
      item: {
        label: 'Mission Command, Cyber, Intelligence Division (MC2ID)',
        value: 'Mission Command, Cyber, Intelligence Division (MC2ID)',
      },
    },
    {
      item: {
        label: 'Maneuver Support & Protection Division (MSPD)',
        value: 'Maneuver Support & Protection Division (MSPD)',
      },
    },
    { item: { label: 'Ram Engineering Division (RED)', value: 'Ram Engineering Division (RED)' } },
    { item: { label: 'Sustainment Division (SUSD)', value: 'Sustainment Division (SUSD)' } },
  ],
  'Joint Modernization Command (JMC)': [
    {
      item: { label: 'Command Group', value: 'Command Group' },
      children: [
        { item: { label: 'Command Group (No Subcategory)', value: 'Command Group (No Subcategory)' } },
        { item: { label: 'Ops Group A', value: 'Ops Group A' } },
        { item: { label: 'Ops Group B', value: 'Ops Group B' } },
        { item: { label: 'Ops Group Z', value: 'Ops Group Z' } },
        {
          item: { label: 'Network Integration Division (NID) / G6', value: 'Network Integration Division (NID) / G6' },
        },
      ],
    },
    {
      item: { label: 'G-3/5/7 Operations', value: 'G-3/5/7 Operations' },
      children: [
        { item: { label: 'G-3/5/7 Operations (No Subcategory)', value: 'G-3/5/7 Operations (No Subcategory)' } },
        {
          item: {
            label: 'Multi-Domain Operations (MDO) Simulation Center',
            value: 'Multi-Domain Operations (MDO) Simulation Center',
          },
        },
        { item: { label: '(UK) Multi-National Integration', value: '(UK) Multi-National Integration' } },
        {
          item: { label: 'Global Operations Team', value: 'Global Operations Team' },
          children: [
            {
              item: {
                label: 'Global Operations Team (No Subcategory)',
                value: 'Global Operations Team (No Subcategory)',
              },
            },
            { item: { label: 'JMC Forward', value: 'JMC Forward' } },
            { item: { label: 'USAF', value: 'USAF' } },
            { item: { label: 'U.S. Marine Corps Warfighting Lab', value: 'U.S. Marine Corps Warfighting Lab' } },
            { item: { label: 'U.S. Army Pacific Command', value: 'U.S. Army Pacific Command' } },
            { item: { label: 'U.S. Army Europe – Africa', value: 'U.S. Army Europe – Africa' } },
          ],
        },
      ],
    },
    {
      item: {
        label: 'Network Operations Support Center (NOSC)',
        value: 'Network Operations Support Center (NOSC)',
      },
    },
    { item: { label: 'Exercise Control Center (ECC)', value: 'Exercise Control Center (ECC)' } },
  ],
  'Medical CDID': [
    { item: { label: 'MED CDID Concepts Division', value: 'MED CDID Concepts Division' } },
    { item: { label: 'MED CDID Experimentation Division', value: 'MED CDID Experimentation Division' } },
    { item: { label: 'MED CDID Requirements Division', value: 'MED CDID Requirements Division' } },
    {
      item: {
        label: 'MED CDID Medical Evacuation Concepts & Capabilities Division',
        value: 'MED CDID Medical Evacuation Concepts & Capabilities Division',
      },
    },
    { item: { label: 'MED CDID Operations Division', value: 'MED CDID Operations Division' } },
    { item: { label: 'ACM Army Health Services (ACM-AHS)', value: 'ACM Army Health Services (ACM-AHS)' } },
  ],
  'Aviation CDID': [
    {
      item: { label: 'Aviation CDID HQ', value: 'Aviation CDID HQ' },
      children: [
        { item: { label: 'Aviation CDID HQ (No Subcategory)', value: 'Aviation CDID HQ (No Subcategory)' } },
        { item: { label: 'Integration Cell', value: 'Integration Cell' } },
        { item: { label: 'Program Management', value: 'Program Management' } },
      ],
    },
    {
      item: {
        label: 'Aviation Enablers - Requirements Determination Directorate (AE-RDD)',
        value: 'Aviation Enablers - Requirements Determination Directorate (AE-RDD)',
      },
      children: [
        {
          item: {
            label: 'Aviation Enablers - Requirements Determination Directorate (AE-RDD) (No Subcategory)',
            value: 'Aviation Enablers - Requirements Determination Directorate (AE-RDD) (No Subcategory)',
          },
        },
        {
          item: {
            label: 'Aviation (AVN) Mission (MSN) Command and Networks Division',
            value: 'Aviation (AVN) Mission (MSN) Command and Networks Division',
          },
        },
        { item: { label: 'Survivability Systems Division', value: 'Survivability Systems Division' } },
        { item: { label: 'Navigation Systems Division', value: 'Navigation Systems Division' } },
        { item: { label: 'Aviation Logistics', value: 'Aviation Logistics' } },
      ],
    },
    {
      item: { label: 'ACM-UAS', value: 'ACM-UAS' },
      children: [
        { item: { label: 'ACM-UAS (No Subcategory)', value: 'ACM-UAS (No Subcategory)' } },
        { item: { label: 'UAS Futures Division', value: 'UAS Futures Division' } },
        { item: { label: 'UAS Current Division', value: 'UAS Current Division' } },
      ],
    },
    {
      item: {
        label: 'Aviation Platforms - Requirements Determination Directorate (AP-RDD)',
        value: 'Aviation Platforms - Requirements Determination Directorate (AP-RDD)',
      },
    },
    {
      item: {
        label: 'Concepts Experiments and Analysis Directorate (CEAD)',
        value: 'Concepts Experiments and Analysis Directorate (CEAD)',
      },
      children: [
        {
          item: {
            label: 'Concepts Experiments and Analysis Directorate (CEAD) (No Subcategory)',
            value: 'Concepts Experiments and Analysis Directorate (CEAD) (No Subcategory)',
          },
        },
        { item: { label: 'Concepts Division', value: 'Concepts Division' } },
        { item: { label: 'Experimentation Division', value: 'Experimentation Division' } },
        { item: { label: 'Analysis Division', value: 'Analysis Division' } },
      ],
    },
    { item: { label: 'Threat Branch', value: 'Threat Branch' } },
    { item: { label: 'ACM- Recon/Attack', value: 'ACM- Recon/Attack' } },
    {
      item: {
        label: 'Aviation Survivability Development and Tactics (ASDAT)',
        value: 'Aviation Survivability Development and Tactics (ASDAT)',
      },
    },
    { item: { label: 'ACM - Lift', value: 'ACM - Lift' } },
    {
      item: {
        label: 'Organization and Personnel Force Development (OPFD)',
        value: 'Organization and Personnel Force Development (OPFD)',
      },
      children: [
        {
          item: {
            label: 'Organization and Personnel Force Development (OPFD) (No Subcategory)',
            value: 'Organization and Personnel Force Development (OPFD) (No Subcategory)',
          },
        },
        {
          item: {
            label: 'Personnel Force Development Officer Division',
            value: 'Personnel Force Development Officer Division',
          },
        },
        { item: { label: 'Warrant Officer Division', value: 'Warrant Officer Division' } },
        { item: { label: 'Enlisted Division', value: 'Enlisted Division' } },
        { item: { label: 'Organization Force Dev Division', value: 'Organization Force Dev Division' } },
      ],
    },
    {
      item: {
        label: 'Army Aviation and Missile Command (AMCOM) Liaison Officer (LNO)',
        value: 'Army Aviation and Missile Command (AMCOM) Liaison Officer (LNO)',
      },
    },
    {
      item: {
        label: 'Combat Capabilities Development Command (CCDC) (DEVCOM) Touchpoints',
        value: 'Combat Capabilities Development Command (CCDC) (DEVCOM) Touchpoints',
      },
    },
    { item: { label: 'AFC-FCC/G3-5-7/ANALYSIS Directorate', value: 'AFC-FCC/G3-5-7/ANALYSIS Directorate' } },
    {
      item: {
        label: 'Reliability, Availability, Maintainability Engineer',
        value: 'Reliability, Availability, Maintainability Engineer',
      },
    },
    { item: { label: 'B Company 1-13th', value: 'B Company 1-13th' } },
  ],
  'Maneuver CDID': [
    {
      item: { label: 'MCDID Concepts Development Division', value: 'MCDID Concepts Development Division' },
    },
    { item: { label: 'M CDID Soldier Requirements Division', value: 'M CDID Soldier Requirements Division' } },
    { item: { label: 'M CDID Maneuver Requirements Division', value: 'M CDID Maneuver Requirements Division' } },
    { item: { label: 'M CIDD Robotics Requirements Division', value: 'M CIDD Robotics Requirements Division' } },
    {
      item: {
        label: 'M CDID Mission Command Requirements Division',
        value: 'M CDID Mission Command Requirements Division',
      },
    },
    { item: { label: 'M CDID Maneuver Battle Lab', value: 'M CDID Maneuver Battle Lab' } },
    {
      item: {
        label: 'Maneuver ACM – Infantry Brigade Combat Team (IBCT)',
        value: 'Maneuver ACM – Infantry Brigade Combat Team (IBCT)',
      },
    },
    {
      item: {
        label: 'Maneuver ACM – Stryker Brigade Combat Team (SBCT)',
        value: 'Maneuver ACM – Stryker Brigade Combat Team (SBCT)',
      },
    },
    {
      item: {
        label: 'Maneuver ACM – Armored Brigade Combat Team (ABCT)',
        value: 'Maneuver ACM – Armored Brigade Combat Team (ABCT)',
      },
    },
    {
      item: {
        label: 'Maneuver ACM – Security Force Assistance Brigade (SFAB)',
        value: 'Maneuver ACM – Security Force Assistance Brigade (SFAB)',
      },
    },
  ],
  'Maneuver Support CDID': [
    { item: { label: 'HQ / Integration Division', value: 'HQ / Integration Division' } },
    { item: { label: 'Concepts Division', value: 'Concepts Division' } },
    { item: { label: 'Experimentation Division', value: 'Experimentation Division' } },
    { item: { label: 'Requirements Determination Division', value: 'Requirements Determination Division' } },
  ],
  'Chaplain CDID': [
    // No specific ACM options provided for Chaplain CDID
  ],
  'Mission Command CDID': [
    {
      item: { label: 'MC CDID Battle Lab', value: 'MC CDID Battle Lab' },
    },
    {
      item: {
        label: 'CRD Concepts & Requirements Division (CRD)',
        value: 'CRD Concepts & Requirements Division (CRD)',
      },
    },
    {
      item: {
        label: 'ACM MC/MP - Army Capabilities Management Mission Command / Command Post (ACM MC/CP)',
        value: 'ACM MC/MP - Army Capabilities Management Mission Command / Command Post (ACM MC/CP)',
      },
    },
  ],
  'Fires CDID': [
    { item: { label: 'Fires CDID Concept Development Division', value: 'Fires CDID Concept Development Division' } },
    {
      item: {
        label: 'Fires CDID Requirements Determination Division',
        value: 'Fires CDID Requirements Determination Division',
      },
    },
    { item: { label: 'FIRES CDID Fires Battle Lab', value: 'FIRES CDID Fires Battle Lab' } },
    { item: { label: 'FIRES CDID Fires Acquisition', value: 'FIRES CDID Fires Acquisition' } },
    { item: { label: 'FIRES CDID Integration Cell', value: 'FIRES CDID Integration Cell' } },
    {
      item: {
        label: 'Fires CDID New Equipment Training (NET) -IT',
        value: 'Fires CDID New Equipment Training (NET) -IT',
      },
    },
    { item: { label: 'FIRES ACM ADA Brigade', value: 'FIRES ACM ADA Brigade' } },
    { item: { label: 'FIRES ACM AAMDC', value: 'FIRES ACM AAMDC' } },
    { item: { label: 'FIRES ACM Fires Cell – Targeting', value: 'FIRES ACM Fires Cell – Targeting' } },
    { item: { label: 'FIRES ACM BCT Fires', value: 'FIRES ACM BCT Fires' } },
    { item: { label: 'FIRES ACM FAB-D', value: 'FIRES ACM FAB-D' } },
  ],
  'Cyber CDID': [
    {
      item: { label: 'Cyber CDID Operations Branch', value: 'Cyber CDID Operations Branch' },
    },
    { item: { label: 'Cyber CDID Program Management Division', value: 'Cyber CDID Program Management Division' } },
    {
      item: {
        label: 'Cyber CDID Concepts and Analysis Division',
        value: 'Cyber CDID Concepts and Analysis Division',
      },
    },
    {
      item: {
        label: 'Cyber CDID Requirements Integration Division',
        value: 'Cyber CDID Requirements Integration Division',
      },
    },
    { item: { label: 'Cyber CDID Cyber Battle Lab', value: 'Cyber CDID Cyber Battle Lab' } },
    { item: { label: 'Cyber ACM - Tactical Radios', value: 'Cyber ACM - Tactical Radios' } },
    { item: { label: 'Cyber ACM - Network & Services', value: 'Cyber ACM - Network & Services' } },
    { item: { label: 'Cyber ACM - Electromagnetic Warfare', value: 'Cyber ACM - Electromagnetic Warfare' } },
    { item: { label: 'Cyber ACM – Cyber', value: 'Cyber ACM – Cyber' } },
  ],
  'Intelligence CDID': [
    {
      item: {
        label: 'Intelligence CDID - Requirements Determination Division (RDD)',
        value: 'Intelligence CDID - Requirements Determination Division (RDD)',
      },
    },
    {
      item: {
        label: 'Intelligence CDID - Intelligence Battle Lab (IBL)',
        value: 'Intelligence CDID - Intelligence Battle Lab (IBL)',
      },
    },
    {
      item: {
        label: 'Intelligence CDID ACM - Formations Intelligence (ACM-FI)',
        value: 'Intelligence CDID ACM - Formations Intelligence (ACM-FI)',
      },
    },
    {
      item: {
        label: 'Intelligence CDID ACM - Intelligence Sensors (ACM-IS)',
        value: 'Intelligence CDID ACM - Intelligence Sensors (ACM-IS)',
      },
    },
    {
      item: {
        label: 'Intelligence CDID ACM - Foundation (ACM-F)',
        value: 'Intelligence CDID ACM - Foundation (ACM-F)',
      },
    },
  ],
  'Sustainment CDID': [
    {
      item: { label: 'Requirements Division', value: 'Requirements Division' },
      children: [
        { item: { label: 'Requirements Division (No Subcategory)', value: 'Requirements Division (No Subcategory)' } },
        { item: { label: 'Distribution Branch', value: 'Distribution Branch' } },
        { item: { label: 'S&T Branch', value: 'S&T Branch' } },
        {
          item: { label: 'Ordnance Branch (OD Branch)', value: 'Ordnance Branch (OD Branch)' },
          children: [
            {
              item: {
                label: 'Ordnance Branch (OD Branch) (No Subcategory)',
                value: 'Ordnance Branch (OD Branch) (No Subcategory)',
              },
            },
            {
              item: {
                label: 'Test, Measurement, and Diagnostic Equipment Team (TMDE TM)',
                value: 'Test, Measurement, and Diagnostic Equipment Team (TMDE TM)',
              },
            },
            { item: { label: 'Ammunition Team (AMMO TM)', value: 'Ammunition Team (AMMO TM)' } },
            { item: { label: 'Ground Maintenance Team (GRD MNT TM)', value: 'Ground Maintenance Team (GRD MNT TM)' } },
            {
              item: {
                label: 'Battlefield Recovery Team (BTLFLD Recovery)',
                value: 'Battlefield Recovery Team (BTLFLD Recovery)',
              },
            },
          ],
        },
        {
          item: { label: 'Quartermaster Branch (QM Branch)', value: 'Quartermaster Branch (QM Branch)' },
          children: [
            {
              item: {
                label: 'Quartermaster Branch (QM Branch) (No Subcategory)',
                value: 'Quartermaster Branch (QM Branch) (No Subcategory)',
              },
            },
            { item: { label: 'Liquid Team (Liquid TM)', value: 'Liquid Team (Liquid TM)' } },
            {
              item: {
                label: 'Sustainment Systems Team (Sustainment Systems TM)',
                value: 'Sustainment Systems Team (Sustainment Systems TM)',
              },
            },
          ],
        },
      ],
    },
    {
      item: {
        label: 'Sustainment Concepts and Battle Lab Division',
        value: 'Sustainment Concepts and Battle Lab Division',
      },
      children: [
        {
          item: {
            label: 'Sustainment Concepts and Battle Lab Division (No Subcategory)',
            value: 'Sustainment Concepts and Battle Lab Division (No Subcategory)',
          },
        },
        { item: { label: 'Concepts Branch', value: 'Concepts Branch' } },
        { item: { label: 'Sustainment Battle Lab Branch', value: 'Sustainment Battle Lab Branch' } },
      ],
    },
    {
      item: {
        label: 'Operational Research / Systems Analysis Cell (ORSA Cell)',
        value: 'Operational Research / Systems Analysis Cell (ORSA Cell)',
      },
      children: [
        {
          item: {
            label: 'Operational Research / Systems Analysis Cell (ORSA Cell) (No Subcategory)',
            value: 'Operational Research / Systems Analysis Cell (ORSA Cell) (No Subcategory)',
          },
        },
        { item: { label: 'Battle Lab Team (Battle Lab TM)', value: 'Battle Lab Team (Battle Lab TM)' } },
        { item: { label: 'Requirement Team (Requirement TM)', value: 'Requirement Team (Requirement TM)' } },
      ],
    },
    {
      item: { label: 'Assessment & Integration Branch', value: 'Assessment & Integration Branch' },
      children: [
        {
          item: {
            label: 'Assessment & Integration Branch (No Subcategory)',
            value: 'Assessment & Integration Branch (No Subcategory)',
          },
        },
        { item: { label: 'Assessments Team (Assessments TM)', value: 'Assessments Team (Assessments TM)' } },
        { item: { label: 'Integration Team (Integration TM)', value: 'Integration Team (Integration TM)' } },
      ],
    },
    {
      item: { label: 'Operations Cell (OPS Cell)', value: 'Operations Cell (OPS Cell)' },
      children: [
        {
          item: {
            label: 'Operations Cell (OPS Cell) (No Subcategory)',
            value: 'Operations Cell (OPS Cell) (No Subcategory)',
          },
        },
        { item: { label: 'XO', value: 'XO' } },
        {
          item: {
            label: 'Operations / Future Operations (OPS / FUOPS)',
            value: 'Operations / Future Operations (OPS / FUOPS)',
          },
        },
        {
          item: {
            label: 'Operations Chief Admin Non-Commissioned Officer in Charge (OPS Chief Admin NCOIC)',
            value: 'Operations Chief Admin Non-Commissioned Officer in Charge (OPS Chief Admin NCOIC)',
          },
        },
        { item: { label: 'Admin CIV', value: 'Admin CIV' } },
        { item: { label: 'IT CTR', value: 'IT CTR' } },
      ],
    },
  ],
};

export const ACMDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: ACMDropdownProps) => {
    const selectedCDID = opportunityStore.opportunity?.capabilitySponsor;

    const localStore = useLocalObservable(() => ({
      isACMDialogOpen: false,
      setIsACMDialogOpen(value: boolean) {
        this.isACMDialogOpen = value;
      },
      get defaultACMValues() {
        const acms = opportunityStore.opportunity?.armyCapabilityManager || [];
        return [
          {
            fieldName: 'acmLevel1',
            label: 'Army Capability Manager (ACM)',
            value: acms[0]
              ? {
                  label: acms[0],
                  value: acms[0],
                }
              : undefined,
          },
          {
            fieldName: 'acmLevel2',
            label: undefined,
            value: acms[1]
              ? {
                  label: acms[1],
                  value: acms[1],
                }
              : undefined,
          },
          {
            fieldName: 'acmLevel3',
            label: undefined,
            value: acms[2]
              ? {
                  label: acms[2],
                  value: acms[2],
                }
              : undefined,
          },
        ];
      },
    }));

    // Only show if a CDID is selected and has ACM options
    if (!selectedCDID || selectedCDID === 'Unassigned' || ACM_OPTIONS_BY_CDID[selectedCDID].length === 0) {
      return null;
    }

    return (
      <>
        <LabeledDependentDropdown
          labelText={'Army Capability Manager (ACM)'}
          key={opportunityStore.opportunity?.capabilitySponsor}
          dropdownMenuProps={{
            getEditable: () => editable,
            theme,
            getMenuGroups: () => {
              return ACM_OPTIONS_BY_CDID[selectedCDID] || [];
            },
            onItemSelected: (item, fieldName) => {
              const currentACMs = opportunityStore.opportunity?.armyCapabilityManager || [];
              let newACMs = [...currentACMs];

              if (fieldName === 'acmLevel1') {
                newACMs = item?.value ? [item.value] : [];
              } else if (fieldName === 'acmLevel2') {
                if (newACMs.length > 0 && item?.value) {
                  newACMs = [newACMs[0], item.value];
                }
              } else if (fieldName === 'acmLevel3') {
                if (newACMs.length > 1 && item?.value) {
                  newACMs = [newACMs[0], newACMs[1], item.value];
                }
              }

              opportunityStore.setValue('armyCapabilityManager', newACMs);
              handleDebounce(opportunityStore);
            },
            defaultValues: localStore.defaultACMValues,
          }}
        />
        <ACMDialog
          getVisible={() => localStore.isACMDialogOpen}
          onConfirm={() => localStore.setIsACMDialogOpen(false)}
          style={{}}
        />
      </>
    );
  }),
);
