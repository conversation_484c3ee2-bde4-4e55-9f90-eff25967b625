import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button } from '../../../lib';
import { Spinner } from '../../../lib/ui/atoms/Spinner';
import { OpportunityStore } from '../../../stores';
import { QueryObserver } from '../../../lib/stores/Store';
import React from 'react';
import { ModalActionNeeded } from '../../../appComponents/modals/ModalActionNeeded';
import { CreateNewProjectDialog } from './CreateNewProjectDialog';
import { CreateProjectFromOpportunityInput } from '../../../services/codegen/types';

interface ButtonProps {
  buttonText: string;
  buttonColor?: string;
  onPress?: () => void;
  iconName?: string;
  showSpinner?: boolean;
}

interface OpportunityFabProps {
  queryObserver: QueryObserver;
  opportunityStore: OpportunityStore;
  theme: ReactNativePaper.ThemeProp;
  onProjectCreated: (id: string) => void;
}

export const OpportunityFab = withTheme(
  observer(({ queryObserver, opportunityStore, theme, onProjectCreated }: OpportunityFabProps) => {
    const {
      styles: { margins, fontSizes },
      colors,
    } = theme;

    const {
      isProjectModalVisible,
      resetLocalState,
      toggleIsProjectModalVisible,
      isActionModalVisible,
      toggleIsActionModalVisible,
    } = useLocalObservable(() => ({
      isProjectModalVisible: false,
      toggleIsProjectModalVisible() {
        this.isActionModalVisible = !this.isActionModalVisible;
        this.isProjectModalVisible = !this.isProjectModalVisible;
      },
      isActionModalVisible: false,
      toggleIsActionModalVisible() {
        this.isActionModalVisible = !this.isActionModalVisible;
      },
      resetLocalState() {
        this.isActionModalVisible = false;
        this.isProjectModalVisible = false;
      },
    }));

    const { needsAction, editable } = opportunityStore;
    const { anyQueryInProgress } = queryObserver;
    const handleCreateNewProject = async (
      opportunityStore: OpportunityStore,
      input: Partial<CreateProjectFromOpportunityInput>,
      onProjectCreated: (projectId: string) => void,
    ) => {
      try {
        toggleIsProjectModalVisible();
        const projectStore = await opportunityStore.createProjectFromOpportunity(input);
        if (projectStore && projectStore.project) {
          onProjectCreated(projectStore.project.id);
          toggleIsActionModalVisible();
        }
      } catch (e) {
        // error handled by the store
      }
    };
    const setEditable = (makeEditable: boolean) => (opportunityStore.editable = makeEditable);
    // TODO: This needs to be factored into the root button component to include a spinner option.
    const ButtonSpinnerComponent = ({
      buttonText,
      buttonColor = '',
      onPress = () => {},
      iconName = '',
      showSpinner = false,
    }: ButtonProps) => (
      <Button
        type="primary"
        style={buttonColor ? { backgroundColor: buttonColor } : {}}
        iconName={iconName}
        labelStyle={[fontSizes.medium]}
        onPress={onPress}
      >
        {showSpinner && <Spinner color={colors.surface} size={14} style={[margins.RightMS, { alignSelf: 'center' }]} />}
        {buttonText}
      </Button>
    );
    const renderButton = () => {
      if (anyQueryInProgress) {
        const props = { buttonText: 'WORKING...', showSpinner: true };
        return React.createElement(ButtonSpinnerComponent, props);
      }
      if (needsAction) {
        const props = {
          buttonText: 'Action Required',
          buttonColor: colors.negativeColor,
          onPress: toggleIsActionModalVisible,
        };
        return React.createElement(ButtonSpinnerComponent, props);
      }
      if (editable) {
        const props = { buttonText: 'LOCK', iconName: 'lock', onPress: () => setEditable(false) };
        return React.createElement(ButtonSpinnerComponent, props);
      }
      const props = { buttonText: 'UNLOCK', iconName: 'lock-open-outline', onPress: () => setEditable(true) };
      return React.createElement(ButtonSpinnerComponent, props);
    };

    return (
      <View style={[margins.M]}>
        {renderButton()}
        <ModalActionNeeded
          onDismiss={resetLocalState}
          onDone={resetLocalState}
          isVisible={isActionModalVisible}
          opportunityStore={opportunityStore}
          onCreateProjectPrimer={toggleIsProjectModalVisible}
        />
        <CreateNewProjectDialog
          onCreateNewProject={(input) => handleCreateNewProject(opportunityStore, input, onProjectCreated)}
          getVisible={() => isProjectModalVisible}
          onDismiss={resetLocalState}
        />
      </View>
    );
  }),
);
