import { useScrollTo } from '@nandorojo/anchor';
import { observer, useLocalObservable } from 'mobx-react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { OpportunityFormat } from '../../../format/OpportunityFormat';
import { UserFormat } from '../../../format/UserFormat';
import { Button, Dates, Hr, MenuItem, PopupListMenu, Title } from '../../../lib';
import { ClickableText } from '../../../lib/ui/atoms/ClickableText';
import { LabeledValue } from '../../../lib/ui/molecules/LabeledValue';
import { PRIORITY_LABELS } from '../../../lib/ui/organisms/PriorityMenu';
import { Router } from '../../../platform/Router';
import { MainStackParamList } from '../../../routing/screens';
import OpportunityStore from '../../../stores/OpportunityStore';
import { CreateNewProject } from './CreateNewProject';
import { OpportunityOwnerListStore } from '../../../stores/OpportunityOwnerListStore';
import { OpportunityStatusButton } from '../../../lib/ui/molecules/OpportunityStatusButton';
import { OpportunityStatus as OpportunityStatusCodeGen } from '../../../services/codegen/types';
import { Text } from '../../../lib';
import { ModalArchiveOpportunity } from '../../../appComponents/ModalArchiveOpportunity';

interface OpportunityHeaderProps {
  style?: StyleProp<ViewStyle>;
  opportunityStore: OpportunityStore;
  router: Router<MainStackParamList>;
  opportunityOwnerListStore: OpportunityOwnerListStore;
  notCurated?: boolean;
  onProjectCreated: (projectId: string) => void;
  onDownload: () => void;
  theme: ReactNativePaper.ThemeProp;
}

export const OpportunityHeader = withTheme(
  observer(
    ({
      opportunityStore,
      style,
      router,
      onProjectCreated,
      notCurated,
      onDownload,
      opportunityOwnerListStore,
      theme,
    }: OpportunityHeaderProps) => {
      const { scrollTo } = useScrollTo();

      const { opportunity, hasBeenCurated } = opportunityStore;

      if (!opportunity) return null;
      const { colors, fonts, fontSizes, styles } = theme;
      const { margins, paddings, components } = styles;
      const [createdAtDate, createdAtTime] = Dates.asDateAndTimeStringTuple(opportunity?.createdAt) || [];
      const [lastCuratedDate, lastCuratedTime] =
        Dates.asDateAndTimeStringTuple(opportunity?.curationInfo?.lastCurated) || [];

      const isPrivate = opportunityStore.getValue('visibility') === 'PRIVATE';
      const user =
        opportunityOwnerListStore.currentOwner?.owner?.user || opportunityOwnerListStore.initialOwner?.owner?.user;

      const opportunityStatus = opportunity?.status;

      const { isArchiveModalVisible, setIsArchiveModalVisible } = useLocalObservable(() => ({
        isArchiveModalVisible: false,
        setIsArchiveModalVisible(value: boolean) {
          this.isArchiveModalVisible = value;
        },
      }));
      const handleUpdateStatus = (newStatus: OpportunityStatusCodeGen) => {
        opportunityStore.setValue('status', newStatus);
        opportunityStore.saveOpportunity();
      };

      return (
        <View
          style={[
            _styles.contentHeader,
            components.infoSectionStyle,
            { borderTopWidth: 0 },
            components.pageContainerConstraints,
            style,
          ]}
        >
          <View style={[{ flex: 1 }, components.globalPageConstraints]}>
            <ClickableText
              style={[
                paddings.LeftML,
                paddings.TopM,
                { color: colors.mediumGray, letterSpacing: 1 },
                fontSizes.small,
                fonts.regular,
              ]}
              onPress={() => router.goBack()}
            >
              {'<  Return to Dashboard'}
            </ClickableText>
            <View style={[paddings.HorizontalXL, paddings.BottomML]}>
              <View
                style={[
                  { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' },
                  margins.BottomM,
                  margins.TopML,
                ]}
              >
                <View style={[{ flexDirection: 'row' }, margins.BottomM]}>
                  <Title
                    style={[
                      fonts.mediumTitle,
                      {
                        alignSelf: 'center',
                        color: colors.text,
                        textAlign: 'center',
                      },
                    ]}
                  >
                    Innovation Opportunity Submission
                  </Title>
                  {hasBeenCurated ? (
                    <>
                      <OpportunityStatusButton
                        style={[components.auxButton2Style, margins.RightXS, margins.LeftM]}
                        labelStyle={[components.auxButton2TextStyle]}
                        labelText={opportunityStore.getValue('status')}
                        onPress={() => scrollTo('status')}
                      />
                      <Button
                        compact={true}
                        style={[components.auxButton1Style, margins.LeftXS]}
                        labelStyle={components.auxButton1TextStyle}
                        onPress={() => scrollTo('priority')}
                      >
                        {PRIORITY_LABELS[opportunityStore.getValue('priority') as never] || 'NONE'}
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        compact={true}
                        style={[components.auxButton3Style, margins.RightXS, margins.LeftM]}
                        labelStyle={components.auxButton3TextStyle}
                      >
                        NEEDS CURATION
                      </Button>
                    </>
                  )}
                  {isPrivate && (
                    <Button
                      compact={true}
                      style={[components.auxButton3Style, margins.RightXS, margins.LeftM]}
                      labelStyle={components.auxButton3TextStyle}
                      onPress={() => scrollTo('visibility')}
                    >
                      Private
                    </Button>
                  )}
                </View>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      flexWrap: 'wrap',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      gap: 6,
                    },
                    margins.BottomM,
                  ]}
                >
                  {opportunityStatus !== OpportunityStatusCodeGen.Archived && (
                    <CreateNewProject
                      style={[margins.HorizontalS]}
                      opportunityStore={opportunityStore}
                      buttonStyle={[{}]}
                      onProjectCreated={onProjectCreated}
                    />
                  )}
                  {opportunityStatus === OpportunityStatusCodeGen.Archived && (
                    <Button
                      type="primary"
                      style={{
                        backgroundColor: '#258750',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                      onPress={() => {
                        if (opportunityStore.opportunity?.projects.length) {
                          handleUpdateStatus(OpportunityStatusCodeGen.Advanced);
                        } else handleUpdateStatus(OpportunityStatusCodeGen.InCuration);
                      }}
                    >
                      {'RESTORE OPPORTUNITY'}
                    </Button>
                  )}
                  <PopupListMenu
                    style={{ maxWidth: 300 }}
                    getMenuItems={() => {
                      const items = [];
                      const showArchiveItem = opportunityStatus !== OpportunityStatusCodeGen.Archived;
                      items.push({ label: 'Download', value: 'download' });
                      showArchiveItem && items.push({ label: 'Archive Opportunity', value: 'archive' });
                      return items;
                    }}
                    onItemSelected={(item: MenuItem) => {
                      if (item.value === 'download') {
                        onDownload();
                      } else if (item.value === 'archive') setIsArchiveModalVisible(true); // handleUpdateStatus(OpportunityStatusCodeGen.Archived);
                    }}
                    anchor={
                      <Text
                        theme={theme}
                        style={{ paddingHorizontal: 8, fontSize: fontSizes.xLarge.fontSize, fontWeight: 'bold' }}
                      >
                        {'\u22EE'}
                      </Text>
                    }
                  />
                  <ModalArchiveOpportunity
                    isVisible={isArchiveModalVisible}
                    onDismiss={() => setIsArchiveModalVisible(false)}
                    onDone={() => {
                      handleUpdateStatus(OpportunityStatusCodeGen.Archived);
                      setIsArchiveModalVisible(false);
                    }}
                  />
                </View>
              </View>
              <Hr />
              <View style={[{ flexDirection: 'row', justifyContent: 'space-between' }, margins.TopM]}>
                {getHeaderLabelValue(theme, 'Name:', () => (user ? UserFormat.formatUser(user) : 'n/a'), {
                  flex: 2,
                })}
                {getHeaderLabelValue(theme, 'Phone:', () => user?.phone || 'n/a')}
              </View>
              <View style={[{ flexDirection: 'row', justifyContent: 'space-between' }, margins.BottomM]}>
                {getHeaderLabelValue(theme, 'Organization:', () => (user ? UserFormat.formatUserOrg(user) : 'n/a'), {
                  flex: 2,
                })}
                {getHeaderLabelValue(theme, 'Email:', () => (user ? UserFormat.formatEmails(user) : 'n/a'))}
              </View>
              <Hr />
              {hasBeenCurated && (
                <>
                  <View style={[{ flexDirection: 'row', justifyContent: 'space-between' }, margins.TopM]}>
                    {getHeaderLabelValue(
                      theme,
                      'Curator(s):',
                      () => OpportunityFormat.formatOpportunityCurators(opportunity!) || 'n/a',
                      { flex: 2 },
                    )}
                    <View style={[{ flexDirection: 'row', flex: 1, justifyContent: 'flex-end', flexWrap: 'wrap' }]}>
                      {getHeaderLabelValue(
                        theme,
                        'Linked Opportunities:',
                        () => `${opportunity?.relatedOpportunityCount || '0'}`,
                      )}
                    </View>
                  </View>
                  <View style={[{ flexDirection: 'row', justifyContent: 'space-between' }, margins.BottomS]}>
                    <View style={[{ flexDirection: 'row', flex: 2, justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                      {getHeaderLabelValue(theme, 'Submitted:', () => createdAtDate || 'n/a')}
                      {getHeaderLabelValue(theme, 'Last Curated:', () => lastCuratedDate || 'n/a', margins.LeftL)}
                    </View>
                    <View style={[{ flexDirection: 'row', flex: 1, justifyContent: 'flex-end', flexWrap: 'wrap' }]}>
                      {getHeaderLabelValue(
                        theme,
                        'Project Primers:',
                        () => `${opportunity?.projects.length || '0'}`,
                        margins.LeftL,
                      )}
                    </View>
                  </View>
                </>
              )}
            </View>
          </View>
        </View>
      );
    },
  ),
);

const _styles = StyleSheet.create({
  contentHeader: {
    flexDirection: 'column',
  },
});

const getHeaderLabelValue = (
  theme: ReactNativePaper.ThemeProp,
  label: string,
  getValue: () => string,
  style?: StyleProp<ViewStyle>,
) => {
  const { fontSizes, fonts } = theme;
  return (
    <LabeledValue
      style={[style, { flexShrink: 1 }]}
      labelProps={{ textStyle: { ...fontSizes.mediumSmall, ...fonts.medium, flexGrow: 0, flexWrap: 'nowrap' } }}
      textValueProps={{ style: { ...fontSizes.small, flex: 1, flexWrap: 'wrap' } }}
      labelText={label}
      getValue={getValue}
    />
  );
};
