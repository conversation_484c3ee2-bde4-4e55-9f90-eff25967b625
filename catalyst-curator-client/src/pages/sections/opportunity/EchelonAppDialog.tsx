import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { DialogProps, Title } from '../../../lib';
import { ScrollView } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';

interface EchelonAppDialogProps extends DialogProps {
  onConfirm: () => void;
  theme: ReactNativePaper.ThemeProp;
}
export const EchelonAppDialog = withTheme(({ theme, onConfirm, getVisible }: EchelonAppDialogProps) => {
  const {
    styles: { margins, paddings, components, fontSizes, fonts },
    colors,
  } = theme;

  return (
    <ContentDialog
      {...{ onConfirm, theme }}
      noTitleBar={true}
      style={{ borderRadius: 16 }}
      contentStyle={[{ borderRadius: 16, width: 660, height: 270, backgroundColor: theme.colors.background }]}
      getVisible={getVisible}
      showClose={true}
    >
      <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>Echelon Applicability</Title>
      <ScrollView
        style={[margins.TopML]}
        contentContainerStyle={[{}]}
        keyboardShouldPersistTaps="handled"
        contentInsetAdjustmentBehavior="always"
      >
        <Text style={components.messageInfoStyle}>
          This field requires the curation authority to identify at which echelons the proposed problem should be
          deployed. The response should be justified with an assessment of the solution’s proposed impact on mission
          readiness and the anticipated resource requirements for deployment.
        </Text>
      </ScrollView>
    </ContentDialog>
  );
});
