import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { OpportunityStore, UserStore } from '../../../../stores';
import { AttachmentInfo } from '../../../../lib/ui/molecules/FileAttachments';
import { Attachment, Location } from '../../../../services/codegen/types';
import { openWebFileLink } from '../../../../utilities/File';
import { AttachmentsSection } from '../../../../appComponents/AttachmentsSection';
import { LinksSection } from '../../../../appComponents/LinksSection';
import { NotesSection } from '../../../../lib/ui/molecules/NotesSection';
import { Group } from '../../../../lib/ui/atoms/Group';

export type AttachmentLocals = {
  displayName: string;
  notes: string;
  fileName: string;
  mimetype?: string;
  isDialogOpen: boolean;
  editingAttachmentId: string | null;
  setDisplayName(displayName: string): void;
  setNotes(notes: string): void;
  setFileName(fileName: string): void;
  setMimetype(mimetype: string): void;
  setIsDialogOpen(value: boolean): void;
  setEditingAttachmentId(id: string | null): void;
  setPendingFile(file: File | null): void;
  setPendingUri(uri: string): void;
  pendingFile: File | null;
  pendingUri: string;
};

interface OpportunityAttachmentsProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  userStore: UserStore;
}

export const OpportunityAttachments = withTheme(
  observer(
    ({ editable, theme, opportunityStore, label, getLabeledTextInput, userStore }: OpportunityAttachmentsProps) => {
      const {
        styles: { components },
      } = theme;

      const attachmentNotesValue = opportunityStore.getValue('attachmentNotes');

      return (
        <Group title="Attachments" style={[{ zIndex: 2 }]} description={label}>
          <AttachmentsSection
            theme={theme}
            userStore={userStore}
            title="Attachments"
            description={label}
            attachments={(opportunityStore.opportunity?.attachments as AttachmentInfo[]) || []}
            isUploadInProgress={opportunityStore.fileUploadInProgress}
            onRetrieveAttachment={(id) => handleOnRetrieveDocument(opportunityStore, id)}
            onDeleteAttachment={(id) => handleOnDeleteDocument(opportunityStore, id)}
            onAddAttachment={async (result) => handleOnAddDocument(result, opportunityStore)}
            onUpdateAttachment={async (id, displayName, notes) =>
              opportunityStore.updateAttachment(id, displayName, notes)
            }
            editable={editable}
          />
          <LinksSection
            theme={theme}
            userStore={userStore}
            editable={editable}
            links={opportunityStore.opportunity?.links || []}
            onAddLink={(url, name, notes) => handleOnAddLink(opportunityStore, url, name, notes)}
            onDeleteLink={(id) => handleOnDeleteLink(opportunityStore, id)}
            onUpdateLink={(id, url, name, notes) => handleOnUpdateLink(opportunityStore, id, url, name, notes)}
          />
          <NotesSection
            theme={theme}
            editable={editable}
            notesValue={attachmentNotesValue}
            hasNotes={!!attachmentNotesValue}
            renderNotesInput={() =>
              getLabeledTextInput({
                opportunityStore,
                fieldName: 'attachmentNotes',
                labelText: 'Notes About Attached Files',
                textInputProps: {
                  multiline: true,
                  placeholder: 'Additional information about attached files',
                },
                editable,
                style: [components.rowStyle],
              })
            }
          />
        </Group>
      );
    },
  ),
);

async function handleOnDeleteLink(opportunityStore: OpportunityStore, linkId: string) {
  opportunityStore.deleteLink(linkId);
}

async function handleOnAddLink(opportunityStore: OpportunityStore, url: string, name: string, notes: string) {
  await opportunityStore.addLink(url, name, notes);
}

async function handleOnUpdateLink(
  opportunityStore: OpportunityStore,
  linkId: string,
  url: string,
  name: string,
  notes: string,
) {
  await opportunityStore.updateLink(linkId, url, name, notes);
}

const handleOnRetrieveDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  const attachment = opportunityStore.opportunity?.attachments.find((a: Attachment) => a.id === attachmentId);
  opportunityStore.getAttachment(attachmentId).then((loc: Location) => {
    return openWebFileLink(loc.location, attachment?.name || '', attachment?.mimetype || '');
  });
};

const handleOnAddDocument = async (
  result: { file: File; uri: string; displayName?: string; notes?: string },
  opportunityStore: OpportunityStore,
): Promise<void> => {
  await opportunityStore.addAttachment(result);
};

const handleOnDeleteDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  opportunityStore.deleteAttachment(attachmentId);
};
