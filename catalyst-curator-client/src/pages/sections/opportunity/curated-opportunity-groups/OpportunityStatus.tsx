import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';
import { OpportunityStatus as OpportunityStatusCodeGen } from '../../../../services/codegen/types';
import { OpportunityStore } from '../../../../stores';
import { StatusDropdown } from '../StatusDropdown';

interface OpportunityStatusProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const OpportunityStatus = withTheme(
  observer(
    ({ editable, theme, getLabeledTextInput, opportunityStore, label, handleDebounce }: OpportunityStatusProps) => {
      const {
        styles: { components, fonts, fontSizes, margins },
        colors,
      } = theme;

      const isOpportunityArchived = opportunityStore.opportunity?.status === OpportunityStatusCodeGen.Archived;
      return (
        <Group title="Opportunity Status" style={{ zIndex: 2 }} description={label}>
          <View style={[components.rowStyle]}>
            <Target name={'status'}>
              <StatusDropdown editable={editable} handleDebounce={handleDebounce} opportunityStore={opportunityStore} />
              {isOpportunityArchived && (
                <Text theme={theme}>
                  {'Restore the Opportunity using the green button in the header to change the Status'}
                </Text>
              )}
            </Target>
          </View>
          {
            // show in readonly mode if it's not empty
            !editable &&
              opportunityStore.getValue('statusNotes') &&
              getLabeledTextInput({
                opportunityStore,
                fieldName: 'statusNotes',
                labelText: 'Status Notes',
                textInputProps: {
                  multiline: true,
                  placeholder: 'Additional notes about Status',
                },
                style: [components.rowStyle],
                editable,
              })
          }
          {
            // show in edit mode, expanded if data present
            editable && (
              <CollapsibleView
                header={
                  <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ADDITIONAL NOTES</Text>
                }
                contentStyle={[margins.VerticalM]}
                defaultOpen={!!opportunityStore.getValue('statusNotes')}
              >
                {getLabeledTextInput({
                  opportunityStore,
                  fieldName: 'statusNotes',
                  labelText: 'Status Notes',
                  textInputProps: {
                    multiline: true,
                    placeholder: 'Additional notes about Status',
                  },
                  style: [components.rowStyle],
                  editable,
                })}
              </CollapsibleView>
            )
          }
        </Group>
      );
    },
  ),
);
