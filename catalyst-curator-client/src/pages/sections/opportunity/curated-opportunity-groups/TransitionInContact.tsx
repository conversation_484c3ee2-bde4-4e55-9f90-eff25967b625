import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { OpportunityStore } from '../../../../stores';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { Label, LabeledInput, Text } from '../../../../lib';
import { RadioButton } from '../../../../lib/ui/atoms/RadioButton';
import { TransitionInContactDropdown } from '../TransitionInContactDropdown';
import { IsTiCloeType } from '../../../../services/codegen/types';

interface TransitionInContactProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const TransitionInContact = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: TransitionInContactProps) => {
    const {
      styles: { components, fonts, fontSizes, paddings },
    } = theme;

    const getTiCLOEText = () => {
      if (opportunityStore.opportunity?.isTiCLOE === IsTiCloeType.Yes) {
        return 'Yes';
      }
      if (opportunityStore.opportunity?.isTiCLOE === IsTiCloeType.No) {
        return 'No';
      }
      return 'Undefined';
    };
    return (
      <>
        <View style={[components.rowStyle]}>
          <Target name={'IsTransitionInContact'}>
            <Label>Is this a Transformation in Contact (TiC) Line of Effort?</Label>
            <LabeledInput>
              {editable ? (
                <View style={{ flexDirection: 'row' }}>
                  <RadioButton
                    label="Undefined"
                    getChecked={() => opportunityStore.opportunity?.isTiCLOE === IsTiCloeType.Undefined}
                    getValue={() => 'Undefined'}
                    onChecked={() => {
                      opportunityStore.setValue('isTiCLOE', IsTiCloeType.Undefined);
                      handleDebounce(opportunityStore);
                    }}
                  />
                  <RadioButton
                    label="No"
                    getChecked={() => opportunityStore.opportunity?.isTiCLOE === IsTiCloeType.No}
                    getValue={() => 'No'}
                    onChecked={() => {
                      opportunityStore.setValue('isTiCLOE', IsTiCloeType.No);
                      opportunityStore.setValue('transitionInContactLineOfEffort', null);
                      handleDebounce(opportunityStore);
                    }}
                  />
                  <RadioButton
                    label="Yes"
                    getChecked={() => opportunityStore.opportunity?.isTiCLOE === IsTiCloeType.Yes}
                    getValue={() => 'Yes'}
                    onChecked={() => {
                      opportunityStore.setValue('isTiCLOE', IsTiCloeType.Yes);
                      handleDebounce(opportunityStore);
                    }}
                  />
                </View>
              ) : (
                <Text style={[fonts.regular, fontSizes.small, paddings.XS]}>{getTiCLOEText()}</Text>
              )}
            </LabeledInput>
          </Target>
        </View>
        {opportunityStore.opportunity?.isTiCLOE === IsTiCloeType.Yes && (
          <TransitionInContactDropdown
            editable={editable}
            handleDebounce={handleDebounce}
            opportunityStore={opportunityStore}
          />
        )}
      </>
    );
  }),
);
