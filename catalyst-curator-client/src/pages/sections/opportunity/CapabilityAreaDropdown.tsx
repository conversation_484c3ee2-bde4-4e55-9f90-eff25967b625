import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { Label, MultiselectMenu } from '../../../lib';
import { OpportunityStore } from '../../../stores';

interface CapabilityAreaDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const CapabilityAreaItems = [
  { label: 'Aircraft', value: 'Aircraft' },
  { label: 'Air Defense', value: 'Air Defense' },
  { label: 'AR/VR', value: 'AR/VR' },
  { label: 'Artificial Intelligence (AI)', value: 'Artificial Intelligence (AI)' },
  { label: 'Automation (Process)', value: 'Automation (Process)' },
  { label: 'Ammunition (Munitions)', value: 'Ammunition (Munitions)' },
  { label: 'Command and Control (C2)', value: 'Command and Control (C2)' },
  { label: 'Communications', value: 'Communications' },
  { label: 'Counter-Mobility', value: 'Counter-Mobility' },
  { label: 'Cover and Concealment', value: 'Cover and Concealment' },
  { label: 'Data Analytics and Visualization', value: 'Data Analytics and Visualization' },
  { label: 'Direct Fires', value: 'Direct Fires' },
  { label: 'Doctrine, Policy, and Organization', value: 'Doctrine, Policy, and Organization' },
  { label: 'Electronic Warfare (EW)', value: 'Electronic Warfare (EW)' },
  { label: 'Energy and Power', value: 'Energy and Power' },
  { label: 'Facilities', value: 'Facilities' },
  { label: 'Ground Vehicles', value: 'Ground Vehicles' },
  { label: 'Human Performance', value: 'Human Performance' },
  { label: 'Fitness / Wellness', value: 'Fitness / Wellness' },
  { label: 'Indirect Fires', value: 'Indirect Fires' },
  { label: 'ISR', value: 'ISR' },
  { label: 'Logistics', value: 'Logistics' },
  { label: 'Low-Cost Fabrication', value: 'Low-Cost Fabrication' },
  { label: 'Maintenance', value: 'Maintenance' },
  { label: 'Medical', value: 'Medical' },
  { label: 'Mobility', value: 'Mobility' },
  { label: 'Network', value: 'Network' },
  { label: 'Nett Warrior', value: 'Nett Warrior' },
  { label: 'Other', value: 'Other' },
  { label: 'Personnel (Retention)', value: 'Personnel (Retention)' },
  { label: 'Personnel (Protection and Survivability)', value: 'Personnel (Protection and Survivability)' },
  { label: 'Reliability', value: 'Reliability' },
  { label: 'Robotic and Autonomous Systems Common Control', value: 'Robotic and Autonomous Systems Common Control' },
  { label: 'Sensors', value: 'Sensors' },
  { label: 'Soldier Worn / Carried', value: 'Soldier Worn / Carried' },
  { label: 'Soldier Load Reduction', value: 'Soldier Load Reduction' },
  { label: 'Soldier Common CsUAS', value: 'Soldier Common CsUAS' },
  { label: 'Soldier and Small Unit Power (S2UP)', value: 'Soldier and Small Unit Power (S2UP)' },
  { label: 'Sustainment', value: 'Sustainment' },
  { label: 'Training', value: 'Training' },
  { label: 'Tactical Assault Kit (TAK)', value: 'Tactical Assault Kit (TAK)' },
  { label: 'Unmanned Aerial Vehicles - UAVs', value: 'Unmanned Aerial Vehicles - UAVs' },
  { label: 'Unmanned Ground Vehicles - UGVs', value: 'Unmanned Ground Vehicles - UGVs' },
  { label: 'Unmanned Surface Vehicles - USVs', value: 'Unmanned Surface Vehicles - USVs' },
  { label: 'Unmanned Underwater Vehicles - UWVs', value: 'Unmanned Underwater Vehicles - UWVs' },
];

export const CapabilityAreaDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: CapabilityAreaDropdownProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <View style={[components.rowStyle, { marginBottom: 16 }]}>
        <Target name={'CapabilityArea'}>
          <Label>Capability Area </Label>
          <MultiselectMenu
            getMenuItems={() => CapabilityAreaItems}
            getSelectedValues={() => opportunityStore.opportunity?.capabilityArea || []}
            onSelectionChange={(values) => {
              opportunityStore.setValue('capabilityArea', values);
              handleDebounce(opportunityStore);
            }}
            getEditable={() => editable}
            placeholder="Unassigned"
            showSelectAll={false}
          />
        </Target>
      </View>
    );
  }),
);
