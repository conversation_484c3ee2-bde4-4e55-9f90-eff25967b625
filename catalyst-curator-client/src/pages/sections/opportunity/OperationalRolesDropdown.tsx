import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { MenuGroup } from '../../../lib';
import { OpportunityStore } from '../../../stores';
import { LabeledDependentDropdown } from '../../../lib/ui/molecules/LabeledDependentDropdown';

interface OperationalRolesDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const OperationalRolesItems: MenuGroup[] = [
  {
    item: { label: 'Combat Arms', value: 'Combat Arms' },
    children: [
      { item: { label: 'Combat Arms', value: 'Combat Arms' }, isParentSelector: true },
      { item: { label: 'Infantry', value: 'Infantry' } },
      { item: { label: 'Armor', value: 'Armor' } },
      { item: { label: 'Field Artillery', value: 'Field Artillery' } },
      { item: { label: 'Air Defense Artillery', value: 'Air Defense Artillery' } },
      { item: { label: 'Aviation', value: 'Aviation' } },
      { item: { label: 'Special Ops', value: 'Special Ops' } },
    ],
  },
  {
    item: { label: 'Combat Support', value: 'Combat Support' },
    children: [
      { item: { label: 'Combat Support', value: 'Combat Support' }, isParentSelector: true },
      { item: { label: 'Military Intelligence', value: 'Military Intelligence' } },
      { item: { label: 'Signal Corps', value: 'Signal Corps' } },
      { item: { label: 'Chemical Corps', value: 'Chemical Corps' } },
      { item: { label: 'Military Police', value: 'Military Police' } },
      { item: { label: 'Engineers', value: 'Engineers' } },
    ],
  },
  {
    item: { label: 'Combat Service Support', value: 'Combat Service Support' },
    children: [
      { item: { label: 'Combat Service Support', value: 'Combat Service Support' }, isParentSelector: true },
      { item: { label: 'Logistics Corps', value: 'Logistics Corps' } },
      { item: { label: 'Transportation Corps', value: 'Transportation Corps' } },
      { item: { label: 'Medical Corps', value: 'Medical Corps' } },
      { item: { label: 'Ordnance Corps', value: 'Ordnance Corps' } },
      { item: { label: 'Quartermaster Corps', value: 'Quartermaster Corps' } },
      { item: { label: 'Finance Corps', value: 'Finance Corps' } },
      { item: { label: "Adjutant General's Corps", value: "Adjutant General's Corps" } },
    ],
  },
];
export const OperationalRolesDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: OperationalRolesDropdownProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <View style={[components.rowStyle]}>
        <Target name={'OperationalRoles'}>
          <LabeledDependentDropdown
            labelText={'Operational Roles'}
            dropdownMenuProps={{
              getEditable: () => editable,
              theme,
              getMenuGroups: () => {
                return OperationalRolesItems;
              },
              onItemSelected: (item, fieldName, hasChildren) => {
                const currentRoles = opportunityStore.opportunity?.operationalRoles || [];
                let newRoles = [...currentRoles];

                if (fieldName === 'operationalRolesLevel1') {
                  newRoles = item?.value ? [item.value] : [];
                } else if (fieldName === 'operationalRolesLevel2') {
                  if (newRoles.length > 0 && item?.value) {
                    newRoles = [newRoles[0], item.value];
                  }
                }
                if (!hasChildren) {
                  opportunityStore.setValue('operationalRoles', newRoles);
                  handleDebounce(opportunityStore);
                }
              },
              defaultValues: [
                {
                  fieldName: 'operationalRolesLevel1',
                  label: 'Operational Roles',
                  value: {
                    label:
                      (opportunityStore.opportunity?.operationalRoles &&
                        opportunityStore.opportunity.operationalRoles[0]) ||
                      'Unassigned',
                    value:
                      (opportunityStore.opportunity?.operationalRoles &&
                        opportunityStore.opportunity.operationalRoles[0]) ||
                      'unassigned',
                  },
                },
                {
                  fieldName: 'operationalRolesLevel2',
                  label: undefined,
                  value:
                    opportunityStore.opportunity?.operationalRoles && opportunityStore.opportunity.operationalRoles[1]
                      ? {
                          label: opportunityStore.opportunity.operationalRoles[1],
                          value: opportunityStore.opportunity.operationalRoles[1],
                        }
                      : undefined,
                },
              ],
            }}
          />
        </Target>
      </View>
    );
  }),
);
