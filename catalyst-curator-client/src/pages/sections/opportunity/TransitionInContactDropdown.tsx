import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Pressable, View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { DependentDropdown, MenuGroup, MenuItem } from '../../../lib';
import { OpportunityStore } from '../../../stores';
import { LabeledDependentDropdown } from '../../../lib/ui/molecules/LabeledDependentDropdown';

interface TransitionInContactDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}
export const TransitionInContactItems: MenuGroup[] = [
  { item: { label: 'Lethality', value: 'Lethality' } },
  {
    item: { label: 'Protection and Survivability', value: 'Protection and Survivability' },
    children: [
      {
        item: {
          label: 'Protection and Survivability',
          value: 'Protection and Survivability',
        },
        isParentSelector: true,
      },
      {
        item: {
          label: 'Protection and Survivability - Soldier Common CsUAS',
          value: 'Protection and Survivability - Soldier Common CsUAS',
        },
      },
    ],
  },
  {
    item: { label: 'Situational Awareness', value: 'Situational Awareness' },
    children: [
      {
        item: {
          label: 'Situational Awareness',
          value: 'Situational Awareness',
        },
        isParentSelector: true,
      },
      {
        item: {
          label: 'Situational Awareness - Tactical Assault Kit (TAK)',
          value: 'Situational Awareness - Tactical Assault Kit (TAK)',
        },
      },
    ],
  },
  { item: { label: 'Mobility', value: 'Mobility' } },
  { item: { label: 'Human Performance', value: 'Human Performance' } },
  { item: { label: 'Soldier and Small Unit Power (S2UP)', value: 'Soldier and Small Unit Power (S2UP)' } },
  { item: { label: 'Communications', value: 'Communications' } },
  {
    item: { label: 'Robotic and Autonomous Systems', value: 'Robotic and Autonomous Systems' },
    children: [
      {
        item: {
          label: 'Robotic and Autonomous Systems',
          value: 'Robotic and Autonomous Systems',
        },
        isParentSelector: true,
      },
      {
        item: {
          label: 'Accelerated Robotic and Autonomous Systems Common Control',
          value: 'Accelerated Robotic and Autonomous Systems Common Control',
        },
      },
    ],
  },
  { item: { label: 'Sustainment', value: 'Sustainment' } },
  { item: { label: 'Soldier Feedback', value: 'Soldier Feedback' } },
  { item: { label: 'Doctrine', value: 'Doctrine' } },
  { item: { label: 'Organization', value: 'Organization' } },
  { item: { label: 'Training', value: 'Training' } },
  { item: { label: 'System Integration ', value: 'System Integration ' } },
  { item: { label: 'Digital Transformation', value: 'Digital Transformation' } },
  { item: { label: 'Data Collection & Optimization', value: 'Data Collection & Optimization' } },
  { item: { label: 'Rapid Prototyping', value: 'Rapid Prototyping' } },
  { item: { label: 'Rapid Fielding', value: 'Rapid Fielding' } },
  { item: { label: 'Acquisition Streamlining', value: 'Acquisition Streamlining' } },
];

export const TransitionInContactDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: TransitionInContactDropdownProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <View style={[components.rowStyle]}>
        <Target name={'TransitionInContact'}>
          <LabeledDependentDropdown
            labelText={'Transition in Contact (TiC) Line of Effort'}
            dropdownMenuProps={{
              getEditable: () => editable,
              theme,
              getMenuGroups: () => {
                return TransitionInContactItems;
              },
              onItemSelected: (item, fieldName, hasChildren) => {
                const currentRoles = opportunityStore.opportunity?.transitionInContactLineOfEffort || [];
                let newTransition = [...currentRoles];

                if (fieldName === 'transitionLevel1') {
                  newTransition = item?.value ? [item.value] : [];
                } else if (fieldName === 'transitionLevel2') {
                  if (newTransition.length > 0 && item?.value) {
                    newTransition = [newTransition[0], item.value];
                  }
                }
                if (!hasChildren) {
                  opportunityStore.setValue('transitionInContactLineOfEffort', newTransition);
                  handleDebounce(opportunityStore);
                }
              },
              defaultValues: [
                {
                  fieldName: 'transitionLevel1',
                  label: 'Transition in Contact (TiC) Line of Effort',
                  value: {
                    label:
                      (opportunityStore.opportunity?.transitionInContactLineOfEffort &&
                        opportunityStore.opportunity.transitionInContactLineOfEffort[0]) ||
                      'Unassigned',
                    value:
                      (opportunityStore.opportunity?.transitionInContactLineOfEffort &&
                        opportunityStore.opportunity.transitionInContactLineOfEffort[0]) ||
                      'unassigned',
                  },
                },
                {
                  fieldName: 'transitionLevel2',
                  label: undefined,
                  value:
                    opportunityStore.opportunity?.transitionInContactLineOfEffort &&
                    opportunityStore.opportunity.transitionInContactLineOfEffort[1]
                      ? {
                          label: opportunityStore.opportunity.transitionInContactLineOfEffort[1],
                          value: opportunityStore.opportunity.transitionInContactLineOfEffort[1],
                        }
                      : undefined,
                },
              ],
            }}
          />
        </Target>
      </View>
    );
  }),
);
