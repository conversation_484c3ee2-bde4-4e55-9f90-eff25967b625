import { Linking, View } from 'react-native';
import { withTheme } from 'react-native-paper';

import { Text } from '../../../lib';
import { IconButton } from '../../../lib/ui/atoms/IconButton';
import { Requirement } from '../../../services/codegen/types';
import { OpportunityStore } from '../../../stores';
import { AddRequirement } from './AddRequirement';

interface RequirementItemProps {
  requirement: Requirement;
  isEditing: boolean;
  editable: boolean;
  opportunityStore: OpportunityStore;
  onEdit: (solutionId: string) => void;
  onCancelEdit: () => void;
  theme: ReactNativePaper.ThemeProp;
}

export const RequirementItem = withTheme(
  ({ requirement, isEditing, editable, opportunityStore, onEdit, onCancelEdit, theme }: RequirementItemProps) => {
    const { colors, fonts } = theme;

    if (isEditing) {
      return (
        <AddRequirement opportunityStore={opportunityStore} defaultRequirement={requirement} setIsOpen={onCancelEdit} />
      );
    }

    return (
      <View
        style={{
          backgroundColor: 'white',
          padding: 12,
          gap: 12,
          borderRadius: 4,
          borderWidth: 1,
          borderColor: colors.border,
        }}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <Text style={fonts.medium}>{requirement.title}</Text>
          </View>
          {editable && (
            <IconButton icon="pencil" color={colors.secondaryTextColor} onPress={() => onEdit(requirement.id)} />
          )}
        </View>
        <Text
          style={{ color: 'blue', textDecorationLine: 'underline' }}
          onPress={() => Linking.openURL(requirement.source)}
        >
          {requirement.source}
        </Text>

        <Text
          style={{ color: 'blue', textDecorationLine: 'underline' }}
          onPress={() => {
            requirement.poc ? Linking.openURL(requirement.poc) : undefined;
          }}
        >
          {requirement.poc}
        </Text>
      </View>
    );
  },
);
