import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { DialogProps, Message, Title } from '../../../lib';
import { Linking, ScrollView, View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';

interface WarfightingFunctionsDialogProps extends DialogProps {
  onConfirm: () => void;
  theme: ReactNativePaper.ThemeProp;
}
export const WarfightingFunctionsDialog = withTheme(
  ({ theme, onConfirm, getVisible }: WarfightingFunctionsDialogProps) => {
    const {
      styles: { margins, paddings, components, fontSizes, fonts },
      colors,
    } = theme;

    return (
      <ContentDialog
        {...{ onConfirm, theme }}
        noTitleBar={true}
        style={{ borderRadius: 16 }}
        contentStyle={[components.warfightingDialogStyle, { borderRadius: 16 }]}
        getVisible={getVisible}
        showClose={true}
      >
        <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>Warfighting Functions</Title>
        <ScrollView
          style={[{ flex: 1 }, margins.TopML]}
          contentContainerStyle={[{ flex: 1 }]}
          keyboardShouldPersistTaps="handled"
          contentInsetAdjustmentBehavior="always"
        >
          <View style={[{ flex: 1, gap: 8 }]}>
            <Text style={components.messageInfoStyle}>
              A Warfighting Function (WFF) will is a group of tasks and systems united by a common purpose that
              commanders use to accomplish missions and training objectives (ADRP 3-0). Warfighting functions are the
              physical means that tactical commanders use to execute operations and accomplish missions assigned by
              superior tactical and operational-level commanders.
            </Text>
            <View style={{}}>
              <Text style={[components.warfightingFunctionListHeader]}>1. Mission Control:</Text>
              <Text style={[components.warfightingFunctionText]}>
                Integrates tasks and systems to balance the art of command with the science of control, enabling
                commanders to utilize other warfighting functions effectively.
              </Text>
            </View>
            <View>
              <Text style={[components.warfightingFunctionListHeader]}>2. Movement & Maneuver:</Text>
              <Text style={[components.warfightingFunctionText]}>
                Involves tasks and systems for moving forces and achieving advantageous positions over adversaries,
                emphasizing direct fire, close combat, and operational maneuver.
              </Text>
            </View>
            <View>
              <Text style={[components.warfightingFunctionListHeader]}>3. Intelligence:</Text>
              <Text style={[components.warfightingFunctionText]}>
                Facilitates understanding of the enemy, terrain, weather, and other significant aspects of the
                operational environment, guiding reconnaissance and surveillance.
              </Text>
            </View>
            <View>
              <Text style={[components.warfightingFunctionListHeader]}>4. Fires:</Text>
              <Text style={[components.warfightingFunctionText]}>
                Coordinates the use of Army indirect fires, air and missile defense, and joint fires through targeting
                to create desired effects on targets.
              </Text>
            </View>
            <View>
              <Text style={[components.warfightingFunctionListHeader]}>5. Sustainment:</Text>
              <Text style={[components.warfightingFunctionText]}>
                Ensures support and services to maintain freedom of action, extend operational reach, and prolong the
                endurance of forces.
              </Text>
            </View>
            <View>
              <Text style={[components.warfightingFunctionListHeader]}>6. Protection:</Text>
              <Text style={[components.warfightingFunctionText]}>
                Involves tasks and systems to preserve the force, allowing commanders to maximize combat power and
                accomplish missions.
              </Text>
            </View>
            <View style={{ flexDirection: 'row' }}>
              <Text>For further reading on Warfighting Functions, </Text>
              <Text
                style={{ color: 'blue', textDecorationLine: 'underline' }}
                onPress={() =>
                  Linking.openURL('https://armypubs.army.mil/epubs/DR_pubs/DR_a/ARN43326-FM_3-0-000-WEB-1.pdf')
                }
              >
                Click here.
              </Text>
            </View>
          </View>
        </ScrollView>
      </ContentDialog>
    );
  },
);
