import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { PieChart } from '../../../appComponents/web/PieChart.web';
import { ChartCard } from './ChartCard';

export type PieChartData = {
  tenant: string;
  [key: string]: number | string;
};

type PieChartVisualProps = {
  data: OpportunityStatusReportResult[] | OpportunityPriorityReportResult[] | OpportunityWFFReportResult[] | undefined;
  theme: ReactNativePaper.ThemeProp;
  title: string;
  tooltipText: string;
  colors?: string[];
  tooltipIcon?: {
    name: string;
    text: string;
  };
};

const statusColors: Record<string, string> = {
  New: '#A358DF',
  'In Curation': '#3268DC',
  'On Hold': '#D8E2F8',
  Archived: '#C4C7CF',
  Closed: '#FFCB00',
  Advanced: '#258750',
};

const defaultColors = ['#3268DC', '#5C7BE2', '#7B8EE8', '#96A2EE', '#AFB6F4', '#C7CBFA', '#DEE0FF'];

export const PieChartVisual = withTheme(
  observer(({ data, title, tooltipText, colors, tooltipIcon }: PieChartVisualProps) => {
    const pieChartData = parsePieChartData(data as unknown as PieChartData[] | undefined);

    const colorsToUse = pieChartData.map((slice) => {
      return (
        statusColors[slice.name] ||
        colors?.[pieChartData.indexOf(slice)] ||
        defaultColors[pieChartData.indexOf(slice) % defaultColors.length]
      );
    });

    return (
      <ChartCard titleText={title} style={{ flex: 1 }} tooltipText={tooltipText} tooltipIcon={tooltipIcon}>
        <PieChart
          data={pieChartData}
          colors={selectColors(colorsToUse, pieChartData.length)}
          defaultIndex={getDefaultIndex(pieChartData)}
          dataKey="total"
        />
      </ChartCard>
    );
  }),
);

function selectColors(colors: string[], n: number): string[] {
  if (n > colors.length) {
    throw new Error('Number of colors requested is more than the available colors');
  }

  if (n === 1) {
    return [colors[0]];
  }

  const result = [];
  const step = (colors.length - 1) / (n - 1);

  for (let i = 0; i < n; i++) {
    const index = Math.round(i * step);
    result.push(colors[index]);
  }

  return result;
}

const mapNames = {
  highPriority: 'High',
  mediumPriority: 'Medium',
  lowPriority: 'Low',
  noPriority: 'None',
  missionCommand: 'Mission Command',
  movementManeuver: 'Movement Maneuver',
  forceProtection: 'Force Protection',
  inCuration: 'In Curation',
  onHold: 'On Hold',
  new: 'New',
  archived: 'Archived',
  closed: 'Closed',
} as Record<string, string>;

const parsePieChartData = (data: PieChartData[] | undefined) => {
  const total = data?.find((item) => item.tenant === 'Total');
  const allOther = data?.filter((item) => item.tenant !== 'Total');
  if (!total || !allOther?.length) return [];
  const keys = Object.keys(allOther[0]).filter((key) => key !== 'tenant');

  const formattedData = keys
    .map((key) => {
      const name = (mapNames[key.split('Count')[0]] as string) || key.split('Count')[0];
      return {
        name,
        tenantBreakdown: allOther.map((result) => {
          return {
            name: result.tenant,
            value: result[key] as number,
          };
        }),
        total: total[key] as number,
      };
    })
    .filter((item) => item.total !== 0);

  return formattedData;
};

const statusHierarchy = ['New', 'In Curation', 'On Hold', 'Closed', 'Archived'];

function getDefaultIndex(pieChartData: ReturnType<typeof parsePieChartData>): number {
  if (pieChartData.length === 0 || pieChartData[0].total === 0) return 0;
  const isWFF = pieChartData[0].name === 'Mission Command';
  const isPriority = pieChartData[0].name === 'High';
  const isStatus = statusHierarchy.some((item) => pieChartData.find((data) => data.name === item));

  if (isStatus) {
    //return top of hierarchy
    const nextWithData = statusHierarchy.find((item) => pieChartData.find((data) => data.name === item));
    return pieChartData.findIndex((item) => item.name === nextWithData);
  }
  if (isWFF) {
    //return highest
    return pieChartData.reduce((acc, cur, index) => (cur.total > pieChartData[acc].total ? index : acc), 0);
  }
  if (isPriority) {
    //return next with data
    return pieChartData.findIndex((item) => item.total !== 0);
  }

  return 0;
}
