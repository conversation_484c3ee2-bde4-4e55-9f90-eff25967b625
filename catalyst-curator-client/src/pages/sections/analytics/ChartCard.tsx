import { ReactNode, useRef } from 'react';
import { Pressable, StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Text } from '../../../lib/ui/atoms/Text';
import { DropdownMenu, DropdownMenuProps, LabeledDropdownMenu, Title } from '../../../lib';
import { Icon } from '../../../lib/ui/atoms/Icon';
import { CampaignFilter } from './CampaignFilter';
import { FilterInfoStore } from '../../../stores/FilterInfoStore';
import { OpportunityReportsStore } from '../../../stores';
import { Tooltip } from '../../../lib/ui/molecules/Tooltip';

interface ChartCardProps {
  style?: StyleProp<ViewStyle>;
  children: ReactNode;
  titleText: string;
  theme: ReactNativePaper.ThemeProp;
  tabs?: { currentTab: string; tabs: { name: string; showContext?: boolean }[]; setTab: (tab: string) => void };
  menuType?: 'select' | 'dots';
  dropdownMenuProps?: DropdownMenuProps;
  eventFilterInfo?: FilterInfoStore;
  opportunityReportsStore?: OpportunityReportsStore;
  tooltipText?: string;
  tooltipIcon?: {
    name: string;
    text: string;
  };
  locals?: { visible: boolean; setVisible: (visible: boolean) => void };
}
export const ChartCard = withTheme(
  ({
    theme,
    children,
    style,
    titleText,
    tabs,
    menuType,
    dropdownMenuProps,
    eventFilterInfo,
    opportunityReportsStore,
    locals,
    tooltipText,
    tooltipIcon,
  }: ChartCardProps) => {
    const {
      colors,
      styles: { components, margins, fonts, paddings, fontSizes },
    } = theme;

    return (
      <>
        <View style={[components.dataCardStyle, components.shadow, style]}>
          <View
            style={[
              paddings.HorizontalML,
              paddings.TopML,
              paddings.BottomM,
              {
                borderBottomColor: colors.border,
                borderBottomWidth: 1,
                flexDirection: 'row',
                justifyContent: 'space-between',
              },
            ]}
          >
            <View style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }}>
              {tooltipText ? (
                <Tooltip text={tooltipText} style={{ justifyContent: 'center' }} hoverEffect>
                  <Text style={[fonts.regularTitle, { alignSelf: 'center' }]}>{titleText}</Text>
                </Tooltip>
              ) : null}
              {tooltipIcon ? (
                <Tooltip text={tooltipIcon.text} style={{ justifyContent: 'center' }} hoverEffect>
                  <Icon name={tooltipIcon.name} color={colors.textSecondary} size={20} />
                </Tooltip>
              ) : null}
            </View>
            {tabs && (
              <View style={{ flexDirection: 'row', flex: 1, justifyContent: 'center' }}>
                {tabs.tabs.map((tab, index) => {
                  return (
                    <Pressable
                      key={index}
                      onPress={() => tabs.setTab(tab.name)}
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: 4,
                      }}
                    >
                      <View
                        style={[
                          margins.RightML,
                          { justifyContent: 'center', alignItems: 'center', flexDirection: 'row', gap: 4 },
                        ]}
                      >
                        <Title
                          style={[
                            theme.fontSizes.small,
                            fonts.regularTitle,
                            margins.LeftML,
                            {
                              color: tabs.currentTab === tab.name ? colors.text : colors.textSecondary,
                              textTransform: 'capitalize',
                            },
                          ]}
                        >
                          {tab.name}
                        </Title>
                        {tab.showContext && (
                          <Tooltip
                            text="Viewing top 5 portfolios based on most recent month with submissions"
                            offsetY={8}
                          >
                            <Icon name="help-circle" color={colors.textSecondary} size={20} />
                          </Tooltip>
                        )}
                      </View>
                      <View
                        style={[
                          tabs.currentTab === tab.name
                            ? { backgroundColor: colors.buttonPrimary }
                            : { backgroundColor: colors.border },
                          { height: 2, width: '100%' },
                        ]}
                      />
                    </Pressable>
                  );
                })}
              </View>
            )}

            {menuType === 'dots' &&
            eventFilterInfo?.info &&
            eventFilterInfo?.info?.length > 0 &&
            opportunityReportsStore &&
            locals ? (
              <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'flex-end' }}>
                <CampaignFilter locals={locals} opportunityReportsStore={opportunityReportsStore} />
              </View>
            ) : menuType === 'select' ? (
              <View>
                <DropdownMenu {...dropdownMenuProps} />
              </View>
            ) : null}
          </View>

          {children}
        </View>
      </>
    );
  },
);
