{"dependencies": {"@expo-google-fonts/poppins": "^0.2.2", "@expo-google-fonts/source-sans-3": "^0.2.3", "@expo/metro-config": "~0.19.0", "@expo/metro-runtime": "~4.0.0", "@nandorojo/anchor": "^0.3.1", "@radix-ui/react-checkbox": "^1.1.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native/metro-config": "^0.73.5", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@urql/core": "^4.2.3", "ag-grid-community": "^29.3.5", "ag-grid-react": "^29.2.0", "babel-plugin-module-resolver": "^5.0.0", "dotenv": "^10.0.0", "expo": "^52.0.0", "expo-blur": "~14.0.1", "expo-constants": "~17.0.3", "expo-document-picker": "~13.0.1", "expo-linking": "~7.0.3", "expo-splash-screen": "~0.29.18", "graphql": "^15.8.0", "html-to-image": "^1.11.11", "mobx": "^6.3.2", "mobx-react": "^7.2.0", "moment": "^2.29.1", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.5", "react-native-gesture-handler": "~2.20.2", "react-native-mask-input": "^1.2.0", "react-native-paper": "^5.12.3", "react-native-paper-dates": "^0.21.9", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-svg-transformer": "^1.3.0", "react-native-web": "~0.19.13", "recharts": "^2.12.6", "smoothscroll-polyfill": "^0.4.4", "uuid": "^8.3.2"}, "devDependencies": {"@graphql-codegen/cli": "^2.0.1", "@graphql-codegen/typescript": "^2.0.0", "@graphql-codegen/typescript-operations": "^2.0.1", "@graphql-codegen/typescript-urql": "^3.0.0", "@types/extract-files": "^8.1.1", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.9.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.1.7", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "typescript": "^5.8.3"}, "scripts": {"postinstall": "node ./postInstall.js", "prod-build-web": "cp prod.env .env; npx expo export -p web --output-dir=web-build --reset-cache", "build-web": "cp sample.env .env; npx expo export -p web --output-dir=web-build --reset-cache", "start": "NODE_OPTIONS=--openssl-legacy-provider expo start", "android": "NODE_OPTIONS=--openssl-legacy-provider expo start --android", "ios": "NODE_OPTIONS=--openssl-legacy-provider expo start --ios", "web": "NODE_OPTIONS=--openssl-legacy-provider expo start --web --reset-cache", "generate": "graphql-codegen", "run-like-prod": "NODE_OPTIONS=--openssl-legacy-provider expo start --no-dev --minify", "lint": "eslint --cache ", "format": "prettier --ignore-path ../.gitignore --write \"src/**/*.+(js|ts|json)\" -w \"../catalyst-lib/src/lib/**/*.+(js|ts|json)\"", "compile": "tsc -p ./tsconfig.json", "prepare": "cd .. && husky catalyst-curator-client/.husky", "test": "jest", "serve": "npx serve web-build --single"}, "lint-staged": {"src/**/*.tsx": ["eslint", "npm run format"], "src/**/*.ts": ["eslint", "npm run format"], "src/**/*.jsx": ["eslint", "npm run format"], "src/**/*.js": ["eslint", "npm run format"]}, "private": true, "homepage": "/ic", "main": "./App.tsx"}